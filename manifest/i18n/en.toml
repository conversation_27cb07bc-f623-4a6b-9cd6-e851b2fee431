# Account Statement
AccountStatementSelectTokenPrompt = "Select the token for which you want to view the statement:"
ErrorFetchingData = "An error occurred while fetching data. Please try again later."
AccountStatementTitle = "Account Statement"
AccountStatementNoRecords = "No transaction records found."
TransactionTypeLabel = "Type"
TransactionAmountLabel = "Amount"
TransactionBalanceLabel = "Balance"
TransactionMemoLabel = "Memo"
# Transaction Types (Add more as needed based on transactions.type column)
TransactionType_transfer = "Transfer"
TransactionType_deposit = "Deposit"
TransactionType_withdrawal = "Withdrawal"
TransactionType_red_packet = "Red Packet"
TransactionType_payment = "Payment"
TransactionType_commission = "Commission"
TransactionType_system_adjust = "System Adjustment"
TransactionType_withdraw="Withdraw"
TransactionType_transfer_out="Transfer Out"
TransactionType_transfer_in="Transfer In"
TransactionType_swap="Swap"
TransactionType_swap_in="Swap In"
TransactionType_swap_out="Swap Out"

# Numeric query related
NoTokensSupportAmount = "❌ No tokens support this transfer amount precision"
NoTokensSupportAmountReceive = "❌ No tokens support this payment request amount precision"
NumericTransferTitle = "Transfer %s %s"
NumericTransferDesc = "Transfer using %s"
NumericReceiveTitle = "Request %s %s"
NumericReceiveDesc = "Create %s payment request"
insufficientBalanceAllTokens = "❌ Insufficient balance in all tokens"

# Withdraw
WithdrawAddressAddSuccess = "✅ Address added successfully!"


# 中文翻译文件 (zh-CN.toml)

# 开始命令回复文本
StartCommandText = '''👋Hi, %s 🆔 %s
——-——-——-——-——-——-——
💰Wallet Balance: %s
🕰Current Time: %s'''

# 按钮文本
DepositButton = "💰 Deposit"
WithdrawButton = "💸 Withdraw"
TransferButton = "⬆️ Transfer"
ReceiveButton = "⬇️ Receive"
RedPacketButton = "🧧 Red Packet"
SwapButton = "🔄 Swap"
ProfileButton = "👤 Profile"
LanguageButton = "🌐 Language"

# New menu buttons
EvolutionGameButton = "🎮 Evolution Game"
PPGameButton = "🎮 PP Game"
PGElectronicsButton = "🎮 PG Electronics"
OfficialGroupButton = "👥 Official Group"
CustomerSupportButton = "🎧 Customer Support"
DepositWithdrawButton = "💰 Deposit/Withdraw"
InviteFriendsButton = "👥 Invite Friends"

# Invite Friends Page
InviteFriendsTitle = "👋 Invite Friends to Join"
InviteLinkLabel = "🔗 Your Exclusive Invite Link:"
InviteRewardsTitle = "📊 Invite Rewards:"
DirectRewardText = "- %s%% commission from direct referrals' betting flow"
IndirectRewardText = "- %s%% commission from indirect referrals' betting flow"
RewardUnlimitedText = "- No limit, lifetime validity"
InviteStatsTitle = "📈 Invite Statistics"
InvitedUsersCount = "- Invited Users: %d"
TotalCommission = "- Total Commission: %.2f CNY"
ViewDirectButton = "👥 Direct Referrals"
ViewIndirectButton = "👥 Indirect Referrals"
DirectSubordinatesTitle = "👥 Your Direct Referrals"
NoSubordinatesMessage = "You haven't invited any users yet. Start inviting friends and earn commission rewards!"
IndirectSubordinatesTitle = "👥 Your Indirect Referrals"
NoIndirectSubordinatesMessage = "You don't have any indirect referrals yet. Users invited by your direct referrals will appear here."
RegistrationTime = "Registration Time"
UnknownUser = "Unknown User"
DetailPrefix = ""
DetailSuffix = " Details"
SubordinateDetailTitle = "👤 Direct Referral Details"
IndirectSubordinateDetailTitle = "👤 Indirect Referral Details"
MonthlyStatisticsTitle = "📊 Monthly Statistics"
TotalDeposits = "Total Deposits"
TotalWithdrawals = "Total Withdrawals"
CommissionEarningsTitle = "💰 Commission Earnings"
CommissionEarned = "Commission Earned"
CommissionEarnedWithRate = "Commission Earned: %s CNY (%.1f%% of total bets)"
OfTotalBets = "of total bets"
ReferredBy = "Referred By"

# Invite Share Inline Message
InviteFriendsDescription = "Share this invite link with friends and earn commission from their bets"
ErrorGettingUserData = "Failed to get user data"
DefaultInvitePromoText = "Join the exciting world of online gaming! Play amazing games, win big rewards, and enjoy exclusive benefits. Your entertainment starts here!"
IAmPrefix = "I am"
InviteBenefit1 = "No registration needed, play instantly!"
InviteBenefit2 = "Big wins, jackpots up to 10,000x!"
InviteBenefit3 = "Earn commission from friend referrals!"
InviteBenefit4 = "Multiple payment methods, safe and convenient!"
StartGameButton = "Start Game"

ShareRewardsButton = "🎁 Share Rewards"
SendRedPacketButton = "🧧 Send Red Packet"

# Deposit/Withdraw page
BalanceLabel = "Balance"
TransactionHistoryButton = "Transaction History"
WithdrawHistoryButton = "Withdrawal History"

# Common messages
ComingSoonMessage = "This feature is under development, stay tuned!"

# Game Display
GamePackageBalance = "Package Balance"
GameBalance = "Game Balance"
CurrentTime = "Current Time"
TransferToGame = "Transfer In"
TransferToWallet = "Transfer Out"
WebVersion = "Web Version"
StartGame = "Start Game"
ReturnHome = "Return Home"
TransferInTitle = "Transfer In"
TransferOutTitle = "Transfer Out"
InsufficientGameBalance = "Insufficient game balance"

# 语言选择
SelectLanguage = "Please select your language:"
LanguageChanged = "🌐 Language changed to Chinese"
BackButton = "◀️ Back"
BackToMainMenu = "◀️ Back to Main Menu"
LanguageSetSuccess = "✅ Language successfully set to %s"

# Permission related error messages
DepositPermissionDenied = "❌ Your account is temporarily unable to use the deposit function"
WithdrawPermissionDenied = "❌ Your account is temporarily unable to use the withdrawal function"
WithdrawNoPaymentPassword = "❌ Please set a payment password first to use the withdrawal function"
TransferPermissionDenied = "❌ Your account is temporarily unable to use the transfer function"
ReceivePermissionDenied = "❌ Your account is temporarily unable to use the receive function"
RedPacketPermissionDenied = "❌ Your account is temporarily unable to use the red packet function"
SwapPermissionDenied = "❌ Your account is temporarily unable to use the swap function"

# System maintenance messages
DepositSystemMaintenance = "🔧 Deposit function is currently under maintenance. Please try again later."
WithdrawSystemMaintenance = "🔧 Withdrawal function is currently under maintenance. Please try again later."
TransferSystemMaintenance = "🔧 Transfer function is currently under maintenance. Please try again later."
ReceiveSystemMaintenance = "🔧 Payment receiving function is currently under maintenance. Please try again later."
RedPacketSystemMaintenance = "🔧 Red packet function is currently under maintenance. Please try again later."
SwapSystemMaintenance = "🔧 Swap function is currently under maintenance. Please try again later."

# 充值流程
DepositSelectCoinPrompt = "Please select the currency you want to deposit:"
DepositSelectNetworkPrompt = "Please select the network type for USDT:"
DepositInfoTitleTRON = "TRON Deposit Address"
DepositInfoTitleETH = "ETH Deposit Address"
DepositSupportedCurrenciesTRON = "Only USDT and TRX on the TRC20 network are supported."
DepositSupportedCurrenciesETH = "Only USDT and ETH on the ERC20 network are supported."
DepositReceivingAddressTRON = "TRON (TRC20) Receiving Address:"
DepositReceivingAddressETH = "ETH (ERC20) Receiving Address:"
DepositQrCodeLinkText = "🖼️ QR Code"
DepositMinAmount = "Minimum deposit amount: %s %s"
# DepositQrWarning = "Please do not deposit any non-designated tokens to this address, otherwise the assets will not be recoverable."
DepositCopyHint = '''
👆 Click to copy the wallet address for repeated deposits!
👆 The address and QR code above are inconsistent, please do not pay!
'''
DepositQrWarning = '''💡 Hint:
- After depositing to the address above👆, the deposit will be successful after 3 network confirmations!
- Please wait patiently, the bot will notify you after the deposit is successful!
'''

# 个人中心
PersonalInfo = "👤 Personal Information"
USDTSymbol = "💵"
TRXSymbol = "💰"
CNYSymbol = "💴"
AccountStatement = "📝 My Statement"
WithdrawalHistory = "📊 Withdrawal History"
SmallAmountExemption = "🔑 Small Amount Exemption"
BackupAccounts = "📱 Backup Accounts"
CNYWithdrawalHistory = "💹 CNY Withdrawal History"
Google2FAButton = "🔐 Google 2FA"

# 备用账户管理
BackupAccountsManagement = "📱 Backup Account Management"
BackupAccountsDescription = "You can add backup accounts to log in when the main account is unavailable. Backup accounts need to be verified with a payment password to be added."
ManageBackupAccounts = "Manage Backup Accounts"
AddBackupAccount = "➕ Add Backup Account"
BackupAccountList = "Backup Account List"
NoBackupAccounts = "You haven't added any backup accounts yet"
DeleteButton = "🗑️ Delete"
ConfirmDeleteBackupAccount = "⚠️ Confirm delete this backup account?"
BackupAccountDeletedSuccess = "✅ Backup account deleted successfully"
PleaseSetPaymentPasswordFirst = "⚠️ Please set a payment password first"
EnterBackupAccountTelegramId = '''
💡 How to get ID?
  - Account ID on the wallet homepage
  - Forward a message to this bot
  - Send any message to @nminfobot
'''
InvalidTelegramIdFormat = "❌ Invalid Telegram ID format, please enter only numbers"
CannotAddSelfAsBackup = "❌ Cannot add yourself as a backup account"
VerifyingPaymentPassword = "⏳ Verifying Payment Password"
PleaseEnterPaymentPassword = "🔑 Please enter your payment password to verify your identity"
OperationTimedOut = "⏱️ Operation timed out, please start again"
BackupAccountAlreadyExists = "❌ This account already exists in the system"
BackupAccountAddedSuccess = "✅ Backup account added successfully"
UserNotOpenedWallet = "❌ User has not opened wallet."
TransferFundsButton = "💸 Transfer Funds"
NoBalanceToTransfer = "❌ Main account has no balance to transfer"
TransferFundsConfirmTitle = "💸 Confirm Fund Transfer"
TransferFundsConfirmMessage = "You are about to transfer all funds from %s account to %s account"
AssetListTitle = "📊 Asset List"
TransferFundsWarning = "⚠️ Important Notice"
TransferFundsWarningMessage = "This operation will transfer all assets and cannot be undone!"
TransferFundsProcessingTitle = "⏳ Processing"
TransferFundsProcessingMessage = "Transferring funds, please wait..."
TransferFundsFailedTitle = "❌ Transfer Failed"
TransferFundsFailedMessage = "Fund transfer failed: %s"
ContactSupportMessage = "Please contact support if you need assistance"
TransferFundsSuccessTitle = "✅ Transfer Successful"
TransferFundsSuccessMessage = "All funds have been successfully transferred to your account!"
TransferFundsSystemError = "System error, please try again later."
TransferFundsAlreadyInProgress = "Fund transfer is already in progress, please try again later."
TransferFundsPasswordTitle = "Enter Payment Password to Complete Transfer"
TransferFundsPasswordMessage = "You are about to transfer all funds from %s account to %s account. Please enter the payment password to confirm."
BackupAccountVerificationTitle = "Backup Account Verification"
BackupAccountVerificationDesc = "%s has added you as a backup account. Please enter %s's payment password to verify"
RequestFrom = "Request from"
RequestTime = "Request time"
BackupAccountVerificationCancelled = "❌ Backup account verification has been cancelled.\n\nIf you need to verify again, please return to the main account to resend the verification request."
SendVerificationMessage = "📤 Send Verification Message"
BackupAccountAlreadyVerified = "✅ This backup account relationship has already been verified"
UnauthorizedOperation = "❌ Unauthorized operation"
PasswordLengthExceeded = "Password length has reached the limit"
PasswordMustBe6Digits = "Password must be 6 digits"
PasswordIncorrect = "❌ Incorrect password, please try again"
BackupAccountVerifiedTitle = "✅ Verification Successful"
BackupAccountVerifiedDesc = "✅ Verification Successful"
PaymentPasswordVerificationFailed = "❌ Payment password verification failed"
Page = "Page"
PrevPage = "⬅️ Previous Page"
NextPage = "➡️ Next Page"
CreatedAt = "Created At"
VerificationStatus = "Verification Status"
Verified = "✅ Verified"
NotVerified = "❌ Not Verified"
Confirm = "✅ Confirm"
Cancel = "❌ Cancel"
Google2FAManagement = "🔐 Google 2FA Management"
Google2FAEnabled = "✅ You have enabled Google 2FA."
Google2FADisabled = "❌ You have not enabled Google 2FA."
Enable2FA = "🔓 Enable 2FA"
Disable2FA = "🔒 Disable 2FA"
Google2FASetup = "Google 2FA Setup"
Google2FAInstructions = "Please scan the QR code or manually enter the following key using Google Authenticator or other TOTP-enabled applications:"
Google2FASecretKey = "🔑 Secret Key:"
Google2FAVerify = "Please enter the six-digit verification code generated by the authenticator:"
Google2FAVerifySuccess = "✅ Verification successful, Google 2FA has been enabled"
Google2FAVerifyFailed = "❌ Verification failed, please try again"
Google2FADisableConfirm = "⚠️ Confirm disable Google 2FA?"
Google2FADisableSuccess = "✅ Google 2FA has been disabled successfully"
Google2FAVerifyInstructions = "Please enter the verification code to complete the setup."
VerifyCode = "✅ Verify and Enable"
CancelButton = "❌ Cancel"
Google2FAAlreadyEnabled = "✅ You have already enabled Google 2FA."
Google2FANotEnabled = "❌ You have not enabled Google 2FA."
Google2FADisabledSuccess = "✅ You have successfully disabled Google 2FA."
Google2FADisableFailed = "❌ Failed to disable Google 2FA, please try again."
PaymentPasswordVerification = "🔑 Payment Password Verification"

# 支付密码
PaymentPasswordButton = "💳 Payment Password"
PaymentPasswordManagement = "🔑 Payment Password Management"
CurrentStatus = "Status"
PaymentPasswordEnabled = "✅ You have set a payment password."
PaymentPasswordDisabled = "❌ You have not set a payment password."
SetPaymentPassword = "🔐 Set Payment Password"
ConfirmSetPaymentPassword = "🔐 Confirm Set Payment Password"
ChangePaymentPassword = "🔄 Change Payment Password"
ResetPaymentPassword = "🗑️ Reset Payment Password"
EnterPaymentPassword = "🔑 Please enter a 6-digit payment password."
EnterCurrentPaymentPassword = "🔑 Please enter your current payment password."
EnterNewPaymentPassword = "🔑 Please enter a new 6-digit payment password."
ConfirmPaymentPassword = "🔑 Please confirm your payment password."
ConfirmNewPaymentPassword = "🔑 Please confirm your new payment password."
PaymentPasswordLengthHint = "⚠️ The password must be 6 digits."
PaymentPasswordFormatInvalid = "❌ Invalid payment password format, please enter 6 digits."
PaymentPasswordSetFailed = "❌ Failed to set payment password, please try again."
PaymentPasswordSetSuccess = "✅ Payment password set successfully"
PaymentPasswordSetSuccessDesc = "✅ You have successfully set your payment password, please keep it safe."
PaymentPasswordAlreadySet = "✅ You have set a payment password, please use the modify function if you need to change it."
PaymentPasswordNotSet = "⚠️ You have not set a payment password, please set one first."
CurrentPaymentPasswordInvalid = "❌ Current payment password incorrect, please try again."
PaymentPasswordChangeFailed = "❌ Failed to change payment password, please try again."
PaymentPasswordChangeSuccess = "✅ Payment password changed successfully"
PaymentPasswordChangeSuccessDesc = "✅ You have successfully changed your payment password, please keep it safe."
ResetPaymentPasswordNoPermission = "❌ You do not have permission to reset the payment password, please contact customer service."
ResetPaymentPasswordSuccess = "✅ Payment password reset successfully"
ResetPaymentPasswordSuccessDesc = "✅ Your payment password has been reset, please set a new payment password."
PaymentPasswordMaxLength = "⚠️ The password cannot exceed 6 digits."
PaymentPasswordEmpty = "⚠️ The password is empty, cannot be deleted."
PaymentPasswordTooShort = "⚠️ The password must be at least 6 digits."
PaymentPasswordIncorrect = "❌ Incorrect password, please try again."
PaymentPasswordMismatch = "❌ The passwords entered do not match, please try again."
PaymentPasswordExactLength = "⚠️ The password must be exactly 6 digits."               # Added for exact length requirement

# 错误提示
ErrorGrpcFailed = "❌ Service request failed, please try again later."
ErrorInvalidSelection = "❌ Invalid selection."
ErrorGeneric = "❌ Operation failed, please try again later."
error_occurred = "❌ An unknown error occurred, please try again later."
InvalidAction = "❌ Invalid action."
UnknownAction = "❓ Unknown action."
DepositInfoTitleTRXTRON = "TRX Deposit Address"
DepositInfoTitleETHETH = "ETH Deposit Address"
DepositInfoTitleUSDTTRON = "TRC20 USDT Deposit Address"
DepositInfoTitleUSDTETH = "ERC20 USDT Deposit Address"
DepositDisabledForAccount = "❌ Your account is disabled for deposit."
DepositDisabledForToken = "❌ Deposit is suspended for this token/network."
AccountSuspended = "❌ Your account has been suspended, please contact customer service."

# 备用账户验证
VerifyBackupAccountTitle = "📱 Verify Backup Account"
EnterMainAccountPaymentPasswordPrompt = "🔑 Please enter the main account's payment password to verify this backup account:"
BackupAccountVerifiedSuccess = "✅ Backup account verified successfully!"
ErrorInvalidRequest = "❌ Error: Invalid request."
ErrorCannotVerifyAccount = "❌ Error: Cannot verify this account at this time."
ErrorTryAgainLater = "❌ An error occurred, please try again later."
ErrorInvalidState = "❌ Error: Invalid operation state, please start again."
ErrorVerificationFailed = "❌ Error: Verification failed."

# Expired operation related
OperationExpiredTitle = "⏱️ Operation Expired"
OperationExpiredMessage = "This operation has timed out, please start again."
TransferPasswordExpiredTitle = "⏱️ Transfer Password Input Expired"
TransferPasswordExpiredMessage = "This transfer password input has timed out\n\nPlease initiate a new transfer"
ReturnToMainMenuButton = "🏠 Return to Main Menu"
ErrorUpdateFailedTryAgain = "❌ Verification successful, but failed to update the status. Please contact support or try again later."
MainMenuButton = "🏠 Main Menu"
VerificationCancelled = "❌ Verification cancelled."

# Backup Accounts Section
MasterAccount = "Main Account"
BackupAccount = "Backup Account"
AddedOn = "Added On"
Unknown = "Unknown"
Back = "◀️ Back"
CannotDeleteMasterAccount = "❌ The main account cannot be deleted"
NotSet = "Not Set"

# Withdraw Section
WithdrawTitle = "💸 Withdraw"
WithdrawSelectSymbol = "Please select the token you want to withdraw:"
WithdrawSelectChain = "Please select %s chain:"
WithdrawSelectAddressType = "Please select how to enter the withdrawal address:"
WithdrawManualAddress = "✍️ Manually Enter Address"
WithdrawWhitelistAddress = "✅ Select from Whitelist"
WithdrawEnterAddress = "Please enter the withdrawal address for %s network %s:"
WithdrawSelectAddress = "Please select an address from your whitelist:"
WithdrawAddNewAddress = "➕ Add New Address"
WithdrawEnterNewAddress = "Please enter the new %s (%s) address to add to the whitelist:"
WithdrawAddressAdded = "✅ Address added to whitelist successfully!"
WithdrawAddressInvalid = "❌ Invalid address format. Please enter a valid %s address."
WithdrawAddressExists = "❌ This address is already in your whitelist."
WithdrawNoAddresses = "❌ You don't have any %s (%s) addresses in your whitelist. Please add a new address."

WithdrawEnterFiatAccount = "Please enter the recipient's account number for %s withdrawal:"

# CNY specific
CNYEnterRecipientName = "Please enter recipient's real name"
RecipientName="👤 Recipient Name"
PleaseUploadQRCode = "Please upload QR code image"
WeChat = "WeChat"
Alipay = "Alipay"
AlipayAccount = "Alipay Account"
PaymentMethod = "Payment Method"
Account = "Account"

WithdrawEnterAmount = "Please enter the amount of %s you want to withdraw:\n\nAvailable Balance: %s %s\nMinimum Withdrawal: %s %s\nMaximum Withdrawal: %s %s\nFee: %s %s"
WithdrawAmountInvalid = "❌ Invalid amount format. Please enter a valid number."
WithdrawAmountTooSmall = "❌ Amount too small. The minimum withdrawal amount is %s %s."
WithdrawAmountTooLarge = "❌ Amount too large. The maximum withdrawal amount is %s %s."
WithdrawInsufficientBalance = "❌ Insufficient balance. Your available balance is %s %s."

WithdrawConfirmation = "Please confirm your withdrawal:\n\n%s\n\nAfter confirmation, you need to enter your payment password."
WithdrawConfirm = "✅ Confirm"
WithdrawCancel = "❌ Cancel"

WithdrawEnterPassword = "🔑 Please enter your payment password to complete the withdrawal:"
WithdrawPasswordIncorrect = "❌ Incorrect payment password. Please try again. Attempts remaining: %s"
WithdrawPasswordLocked = "🔒 Your payment password has been locked due to multiple incorrect attempts. Please try again later."
WithdrawNoPassword = "⚠️ You have not set a payment password. Please go to the Personal Center to set one."
WithdrawNoPasswordPrompt = "⚠️ You have not set a payment password."

WithdrawSuccess = "✅ Withdrawal successful! Your transaction has been submitted.\n\nAmount: %s %s\nFee: %s %s\nFinal Amount: %s %s\nAddress: %s\n\nYou can view the status in Withdrawal History."
WithdrawFailed = "❌ Withdrawal failed: %s"
WithdrawCancelled = "❌ Withdrawal cancelled."

WithdrawNoPermission = "❌ You do not have withdrawal permission. Please contact customer service."
WithdrawMaintenanceMode = "🚧 Withdrawal service is currently under maintenance: %s"

# Withdraw Summary fields
WithdrawCurrency = "💰 Currency"
WithdrawNetwork = "🌐 Network"
WithdrawAddress = "🏘️ Address"
WithdrawRecipientName = "👤 Recipient Name"
WithdrawRecipientAccount = "🏦 Recipient Account"
WithdrawAmount = "💰 Amount"
WithdrawFee = "💸 Fee"
WithdrawFinalAmount = "✅ Final Amount"

# Newly added keys from recent changes
WithdrawSummaryHeader = "💸 Withdrawal Information Summary"
SystemError = "❌ System error, please try again later."
WithdrawStateExpired = "⏱️ Operation timed out or status has expired, please re-initiate the withdrawal."
ErrorFetchingToken = "❌ An error occurred while fetching token information."
TokenNotAvailableForWithdraw = "❌ This token is currently not available for withdrawal."
WithdrawalTemporarilyDisabled = "🚧 Withdrawal is temporarily suspended for this token/network."
ErrorProcessingRequest = "❌ An error occurred while processing your request."
WithdrawAmountFormatInvalid = "❌ Invalid amount format, please enter a valid number."
AmountMustBePositive = "⚠️ The amount must be greater than 0."
FeeCalculationFailed = "❌ Failed to calculate the fee."
AmountLessThanFee = "⚠️ Withdrawal amount is less than the fee."
ErrorSendingConfirmation = "❌ An error occurred while sending the confirmation message, please try again later."
Symbol = "🪙 Token"
Address = "🏘️ Address"
FinalAmount = "✅ Final Amount"
Amount = "💰 Amount"
Fee = "💸 Fee"

# Missing Keys for text_handler.go
WithdrawNoSymbolsAvailable = "❌ No tokens available for withdrawal at this time."
NoPaymentPasswordPrompt = "⚠️ Please set a payment password first to use the red packet function."
FeatureUnderDevelopment = "🚧 Feature under development, please stay tuned."
UnknownCommand = "❓ Unknown command: %s"
EchoPrefix = "Echo: "

WithdrawInvalidAddressFormat = "❌ Invalid address format %s(%s), please re-enter"
WithdrawAddressValidationFailed = "❌ Address validation failed: %s, please re-enter"

# 提现成功消息
WithdrawSubmitSuccess = "✅ Withdrawal application has been successfully submitted!\n\nAmount: %s %s\nNetwork: %s\nAddress: %s\n\nYour withdrawal request has entered the processing queue and will be notified after processing is complete."

# Withdraw history section
withdrawHistoryTitle = "📊 Withdrawal Records"
withdrawQRCode = "Withdrawal QR Code"
withdrawQRCodeAvailable = "Uploaded"
withdrawRecordID = "#️⃣ Order"
withdrawOrderNumber = "📋 Order ID"
withdrawDeductionAmount = "💸 Deduction Amount"
withdrawReceivedAmount = "💰 Received Amount"
withdrawTime = "⏰ Time"
withdrawRemarks = "📝 Remarks"
withdrawAmount = "💰 Amount"
withdrawStatus = "🚦 Status"
withdrawNetwork = "🌐 Network/Chain"
withdrawAddress = "🏘️ Address"
withdrawTxHash = "🔗 Transaction Hash"
withdrawReason = "⛔ Rejection Reason"
withdrawCreatedAt = "⏰ Time"
withdrawStatusPending = "⏳ Pending"
withdrawStatusProcessing = "⚙️ Processing"
withdrawStatusSuccess = "✅ Success"
withdrawStatusFailed = "❌ Failed"
withdrawStatusRejected = "⛔ Rejected"
withdrawStatusUnknown = "❓ Unknown"
withdrawFiatType = "💳 Withdrawal Method"
withdrawFiatTypeAlipayAccount = "Alipay Account"
withdrawFiatTypeAlipayQR = "Alipay QR Code"
withdrawFiatTypeWechatQR = "WeChat QR Code"
buttonPreviousPage = "⬅️ Previous Page"
buttonNextPage = "Next Page ➡️"
buttonBack = "◀️ Back"
buttonMainMenu = "🏠 Main Menu"
noWithdrawRecords = "No withdrawal records yet"
errorParsingCallback = "❌ Error parsing callback data"
errorLoadingWithdrawHistory = "❌ Error loading withdrawal history"
errorFormattingWithdrawRecord = "❌ Error formatting withdrawal record"
errorBuildingKeyboard = "❌ Error creating keyboard"
userNotFound = "❌ User not found"
WithdrawAddressAddFailed = "❌ Failed to add address: %s, please re-enter"
WithdrawAmountBelowMinimum = "❌ Withdrawal amount is below the minimum limit (%s %s)."
transferAmountBelowMinimum = "❌ Transfer amount is below the minimum limit (%s %s)."
transferAmountExceedsMaximum = "❌ Transfer amount exceeds the maximum limit (%s %s)."
transferInsufficientBalance = "❌ Insufficient balance. Your available balance is %s %s."
WhitelistAddress="🏘️ Whitelist Address"
WithdrawPromptPasswordForAddress="🔑 Please enter your payment password to confirm the address"
WithdrawAddressConfirmation="Confirm address: %s"

# 分页相关键
WithdrawSelectAddressPaged = "Select an address from your whitelist (Page %d/%d):"
WithdrawStateExpiredOrInvalid = "⏱️ Operation timed out or state is invalid, please re-initiate withdrawal."


# 转账功能
inlineTransferInstruction = "Please enter @{BotUsername} {Keyword} in the chat window where you want to initiate the transfer (with a friend or in a group) and select the result."
promptTransferAmountGeneric = "Please enter the transfer amount (USDT). For group transfers, please also @mention the recipient (e.g., 100 @username)."
inlineResultTransferTitle = "⬆️ Initiate a Transfer"
inlineResultTransferDesc = "Click here to enter the transfer amount"
errorInlineQueryFailedTitle = "❌ Error"
errorInlineQueryFailedDesc = "❌ Unable to process your request, please try again later."
errorInvalidAmountFormat = "❌ Invalid amount format."
errorCannotDetermineReceiverPrivate = "❌ Unable to determine private chat recipient."
errorParsingAmountMention = "❌ Unable to parse amount or recipient, please enter in the correct format (e.g., {Example})."
errorRecipientNotSpecifiedGroup = "❌ Please @mention the recipient in the group."
errorReceiverNotFound = "❌ Recipient @%s not found."
errorTransferToSelf = "❌ Cannot transfer to yourself."
errorUnsupportedChatType = "❌ Transfer initiation is not supported in this chat type."
errorTransferInitiationFailed = "❌ Failed to initiate transfer, please try again later."
errorInsufficientBalance = "❌ Insufficient account balance."
transferInitiatedInlineSuccess = "✅ Transfer request to %s initiated, please wait for the other party to receive."
errorCannotSendToReceiver = "❌ Unable to send a receive message to the recipient, please contact customer service."
errorCannotFindReceiverChatID = "❌ Could not find the recipient's chat information, please contact customer service."
transferReceived = "✅ Received"
transferExpired = "⏱️ Expired"
transferAlertSender = "⏳ You are the transfer initiator, please wait for the recipient to act."
transferAlertUnauthorized = "⛔ You are not authorized to operate this transfer."
transferSuccessSenderNotify = "✅ You have successfully transferred %s %s to @%s."
transferSuccessReceiverNotify = "✅ @%s has received %s %s from @%s."
transferExpiredSenderNotify = "⏱️ Your transfer request of %s %s to @%s has expired."
transferExpiredReceiverNotify = "⏱️ The transfer request of %s %s from %s has expired."
transferError = "❌ Transfer processing failed: %s"
errorInvalidCallbackData = "❌ Invalid callback data."
errorTransferNotFound = "❌ Transfer record not found."

alertCollectRaceConditionOrInvalid = "⚠️ This transfer has been claimed or its status has changed"


# 转账 - 选择收款人
transferRecipientSelectionPrompt = '''💸 Please use the following methods to select a recipient:

- Send the recipient's @username
- Forward a message from the recipient to here
- Send the recipient's 【Wallet Page】->【Account ID】'''
transferSelectRecipientButton = "👥 Select Recipient"


transferSelectTokenPrompt="Select the token you want to transfer:"

# 内联查询 - 转账确认
# transferSearchResultDesc = "转账 %s %s | 您的余额: %s %s" # Removed, replaced by combined key below
invalidAmountFormat = "❌ Invalid amount format" # 保留
ErrorInvalidQueryFormat = "❌ Invalid query format" # 保留
Error = "❌ Error" # 保留
transferConfirmInlineTitle = "✅ Confirm Transfer" # 保留
# transferConfirmDescSimple = "转账 %s %s" # Removed, replaced by combined key below
transferConfirmDescWithBalanceAndWarning = "❗ Transfer %s %s | Balance: %s %s\n❗ You are transferring to the other party, and it will take effect immediately." # New combined description with balance and warning for preview
# transferConfirmDescWithWarning = "❗转账 %s %s\n❗ 您正在向对方转账 %s 并且立刻生效。" # Keep for message on click
# 移除了 transferConfirmMessage, userNotFoundOrInvalid, invalidRecipientID
# insufficientBalanceTitle = "余额不足" # Removed, replaced by formatted version below
insufficientBalanceTitleWithDetails = "❌ Insufficient balance for %s (Current: %s)" # New formatted title
# insufficientBalanceDesc = "您的 %s 余额不足 (%s)" # Removed, replaced by detailed version below
insufficientBalanceDescDetailed = "❌ Failed to transfer %s %s. Required amount %s %s, available balance %s %s." # New detailed description
errorInvalidTokenSymbol = "❌ Invalid token symbol: %s" # New key for invalid token symbol
OpenBot = "🤖 Open Bot"

transferNeedsVerification = "💰 Transfer to you %s %s\n\nWaiting for the payer to verify the payment password"
verifyPaymentPasswordButton = "🔑 Verify Payment Password"
transferConfirmDescNoWarning = "💰 Transfer to you %s %s"

# 小额免密设置
PassFree_SettingsTitle = "🔑 Small Amount Pass-Free Settings"
PassFree_CurrentLimitFormat = "Current Limit: %s %s"
PassFree_NotSet = "❌ Not Set"
PassFree_SetLimitButtonFormat = "Set %s Limit"
PassFree_ErrorFetchingTokens = "❌ Failed to retrieve the list of supported tokens."
PassFree_NoTokensAvailable = "❌ No tokens available for small amount pass-free settings."
PassFree_ErrorFetchingSettings = "❌ Failed to retrieve the current small amount pass-free settings."
PassFree_ErrorInvalidCallback = "❌ Invalid operation or callback data."
Error_FailedToSetUserState = "❌ Failed to update the operation status, please try again."
PassFree_EnterAmountPromptFormat = "Please enter the new pass-free limit for %s (Current: %s):\n(Enter 0 to disable)"
Error_Internal = "❌ An internal error occurred, please try again later."
PassFree_InvalidAmountFormat = "❌ Invalid amount format, please enter a valid number."
PassFree_AmountCannotBeNegative = "❌ The amount cannot be negative."
PassFree_ErrorSettingLimit = "❌ Failed to set the small amount pass-free limit, please try again."
PassFree_SetSuccessFormat = "✅ The small amount pass-free limit for %s has been successfully set to %s."

# Transfer Module Specific Keys (Generated from Plan)
"promptEnterPassword" = "🔑 Please enter payment password"
"transferStatusPendingPass" = "⏳ Waiting for payer verification"
"transferMessagePendingPass" = "%s initiated a transfer of %s to %s, waiting for the payer to verify their password."
"transferStatusPendingCollection" = "⏳ Awaiting Collection"
"transferMessagePendingCollection" = "You have a transfer of %s %s from %s, waiting to be collected."
"transferStatusCompleted" = "✅ Collected"
"transferMessageCompleted" = "{sender}’s transfer of %s to %s has been collected."
"transferStatusExpired" = "⏱️ Expired"
"transferMessageExpired" = "The transfer of %s from %s to %s has expired."
"transferStatusFailed" = "❌ Processing Failed"
"transferMessageFailed" = "Transfer of %s from %s to %s failed to process."
"transferStatusInsufficientFunds" = "❌ Insufficient Funds"
"transferMessageInsufficientFunds" = "Transfer of %s from %s to %s failed due to insufficient funds."
"transferMessageUnknownStatus" = "❓ Abnormal transfer status: %s"
"transferMemoLabel" = "📝 Memo: %s"
"transferReceivedNotify" = "💰 Received transfer from %s: %s %s"
"transferReceivedNotifyDetailed" = "Received payment %s%s\nFrom: %s\nOrder ID: %s"
"transferConfirmPrompt" = "📤 Please confirm transfer details:\n\nRecipient: %s (ID: %s)\nAmount: %s %s\n\n⚠️ %s\n\nPlease verify the information above."
"transferConfirmButton" = "✅ Confirm Transfer"
"transferCancelButton" = "❌ Cancel"
"alertCollectNotReceiver" = "⛔ You are not the designated recipient of this transfer"
"alertCollectIsSender" = "⛔ You cannot collect a transfer you initiated"
"alertCollectAlreadyCompleted" = "⚠️ This transfer has already been collected"
"alertCollectExpired" = "⏱️ This transfer has expired"
"alertCollectFailedGeneric" = "❌ Transfer processing failed"
"alertCollectInsufficientFunds" = "❌ Sender's balance is insufficient"
"transferSenderInsufficientBalance" = "❌ Transfer cannot be collected: sender's balance is insufficient"
"alertCollectPendingPass" = "⏳ This transfer awaits payer verification"
"alertCollectSuccess" = "✅ Collection successful!"
"alertPasswordLocked" = "🔒 Payment password locked, please try again later"
"alertPasswordIncorrect" = "❌ Incorrect payment password"
"alertPasswordNotSet" = "⚠️ User has not set a payment password"
"alertPasswordVerificationFailed" = "❌ Password verification failed"
"alertPasswordVerified" = "✅ Password verification successful!"
"transferPasswordVerificationCompleted" = "✅ Operation completed"
"transferMessageInitiatedPendingPass" = "%s initiated a transfer of %s %s, please verify your payment password."
"transferMessageInitiatedPendingCollection" = "You have a transfer of %s %s from %s, awaiting collection."
"transferNotAvailable" = "🚧 Transfer function is currently unavailable."

"transferConfirmDescWithBalance"="⬆️ Transfer %s %s | Balance: %s %s"

# Shared Keys (Generated from Plan)
SharedErrorPasswordVerifierServiceUnavailable = "❌ Verification service temporarily unavailable"
SharedErrorPasswordVerifierAccountLocked = "🔒 Account locked, please try again later"
SharedErrorPasswordVerifierAccountLockedWithMinutes = "🔒 Account locked, please try again in %d minutes"
SharedErrorPasswordVerifierGetUserInfoFailed = "❌ Failed to get user information"
SharedErrorPasswordVerifierUserNotFound = "❌ User not found"
SharedErrorPasswordVerifierPasswordNotSet = "⚠️ Payment password not set"
SharedErrorPasswordVerifierTooManyAttempts = "🔒 Too many incorrect password attempts, account locked for %d hours"
SharedErrorPasswordVerifierIncorrectWithAttempts = "❌ Incorrect password, %d attempts remaining"
SharedErrorPasswordVerifierSetPermissionFailed = "❌ Failed to set operation permission"

"collectTransferButton"="👑 Collect"
"EnterTransferPaymentPasswordPrompt"="🔑 Please enter your payment password to complete the transfer:"
"OperationCancelled"="❌ Operation cancelled"
"TransferReadyToCollect"="⏳ Ready to Collect"

"failed_insufficient_funds"="❌ Insufficient funds"

# 转账 - 收款人输入处理
transferInvalidRecipientInputFormat = "❌ Invalid recipient information format. Please send @username, forward a message, or account ID."
transferInvalidRecipientInputType = "❌ Invalid message type received, please send text or forward a message."
transferRecipientNotFound = "❌ The recipient you specified was not found, please check your input or try another method."
transferRecipientFindError = "❌ An error occurred while finding the recipient, please try again later."

# 转账 - 金额与确认流程
transferPromptForAmountWithBalance = "Recipient selected: @%s (ID: %s).\nYour %s balance: %s\nPlease enter the transfer amount:"
transferInvalidAmountFormat = "❌ Invalid amount format, please enter a valid number."
transferAmountTooLow = "❌ The transfer amount must be greater than 0."
transferConfirmationPrompt = "Please confirm the transfer:\nRecipient: @%s (ID: %d)\nAmount: %s %s"
transferPromptForPassword = "You are making a transfer\nAmount: %s %s"
transferIrreversibleWarning = "Notice: This transfer will be completed instantly and cannot be reversed!"
transferCancelled = "❌ Transfer cancelled."
transferSuccessNotify = "✅ Transfer successful!\nYou have transferred %s %s to @%s (ID: %d)."
transferSuccessNotifyDetailed = "Successfully transferred to %s\nUser ID: %d\nName: %s\nUsername: %s\nPayment Amount: %s%s\nOrder ID: %s\nTip: You can forward this payment receipt to the recipient."
transferReceivedNotification = "💰 You received a transfer!\n\nFrom: %s (%s)\nAmount: %s %s\nOrder ID: %s\n\nThe transfer has been credited to your account."
transferFailedNotify = "❌ Transfer failed: %s"
transferRecipientConfirmed = "✅ Recipient selected: %s (ID: %d)."


# Transfer Cancellation
inlineTransferCancelledButton = "❌ Transfer Cancelled"
EnterPasswordForTransferPrompt="🔑 Please enter your payment password to authorize the transfer of %s %s"
# 转账记录功能
TransferHistoryTitle = "📊 Transfer History"
TransferHistoryAllTokens = "All Tokens"
TransferHistoryPageInfo = "Page %d / %d"
TransferHistoryPreviousPage = "⬅️ Previous Page"
TransferHistoryNextPage = "Next Page ➡️"
TransferHistoryBackButton = "◀️ Back"
TransferStatusPending = "⏳ Processing" # 复用现有? 检查是否需要特定翻译
TransferStatusCompleted = "✅ Completed" # 复用现有? 检查是否需要特定翻译
TransferStatusExpired = "⏱️ Expired" # 复用现有? 检查是否需要特定翻译
TransferStatusFailed = "❌ Failed" # 补充完整性，复用现有?
TransferDirectionIn = "📥 Received"
TransferDirectionOut = "📤 Sent"
TransferPeerUnknown = "👤 Unknown User"
TransferHistoryNoRecords = "No transfer records yet"
# 转账记录 - 优化显示
TransferHistoryTypeLabel = "Type"
TransferHistoryRecipientLabel = "👥 Recipient"
TransferHistorySenderLabel = "👤 Sender"
TransferHistoryTokenLabel = "🪙 Token"
TransferHistoryAmountLabel = "💰 Amount"
TransferHistoryStatusLabel = "🚦 Status"
TransferHistoryTimeLabel = "⏰ Time"
TransferHistoryCurrentUserLabel = "👤 Me"

# 红包封面相关
PreviousPage = "⬅️ Previous Page"
Delete = "🗑️ Delete"
UploadImage = "🖼️ Upload Image"
Status = "🚦 Status"
StatusApproved = "✅ Approved"
StatusPendingReview = "⏳ Pending Review"
StatusRejected = "⛔ Rejected"
RejectReasonPrefix = "⛔ Reject Reason:"
NoRedPacketCovers = "❌ You haven't uploaded any red packet cover images. Please click the \"Upload Image\" button to upload."
ConfirmDeleteCover = "⚠️ Are you sure you want to delete this red packet cover? This action is irreversible."
ConfirmDelete = "✅ Confirm Delete"
CoverDeleteSuccess = "✅ Cover deleted successfully"
UserNotFound = "❌ User information not found"
FailedToLoadCovers = "❌ Failed to load covers, please try again"
InvalidPageNumber = "❌ Invalid page number"
InvalidImageID = "❌ Invalid image ID"
SetStateFailed = "❌ Failed to set status, please try again"
UploadCoverPrompt = "🖼️ Please send the image you want to use as the red packet cover"
PleaseUploadImage = "🖼️ Please upload an image"
ImageValidationError = "❌ Image validation failed, please try again"
ImageInvalid = "❌ Invalid image"
UploadFailed = "❌ Upload failed, please try again"
SaveImageFailed = "❌ Failed to save image information, please try again"
CoverUploadSuccess = "✅ Cover uploaded successfully! Your cover is under review and will be available after approval."

# Red Packet Menu
RedPacketMenuTitle = "🧧 Red Packet Menu"
RedPacketOngoingButton = "🎯 Ongoing"
RedPacketEndedButton = "✅ Ended"
RedPacketAllButton = "📋 All"
RedPacketAddButton = "➕ Send Red Packet"
RedPacketSetCoverButton = "🖼️ Cover Management"

UploadRedPacketCoverPrompt = "🖼️ Please send the image you want to use as a red packet cover (%s %s %s %s)"

RedPacketCoverUploadedPendingReview = "✅ Cover uploaded successfully and is pending review. It will be available after approval."
# 红包封面状态
rp_cover_status_pending_review = "⏳ Pending Review"
rp_cover_status_success = "✅ Approved"
rp_cover_status_fail = "❌ Rejected"

ConfirmDeleteRedPacketCover="⚠️ Are you sure you want to delete this red packet cover? This action is irreversible."
# Red Packet Flow
FailedToLoadTokens = "❌ Failed to load token list"
SelectRedPacketTokenTitle = "Please select a red packet token"
InvalidSelection = "❌ Invalid selection"
SelectRedPacketTypeTitle = "Please select red packet type"
SelectedRandomTypeForToken = "Random red packet selected (Token ID: %d)" # %d is placeholder for token ID
SelectedFixedTypeForToken = "Fixed amount red packet selected (Token ID: %d)" # %d is placeholder for token ID
RedPacketTypeRandomButton = "🎲 Random Amount"
RedPacketTypeFixedButton = "💰 Fixed Amount"
# Red Packet Creation Flow
EnterRedPacketQuantity = "Please enter the number of red packets (%d-%d):"
InvalidRedPacketQuantity = "❌ Invalid input, please enter an integer between %d and %d."
RedPacketQuantityExceedsLimit = "❌ Red packet quantity cannot exceed %d, please re-enter."
RedPacketQuantityBelowMinimum = "❌ Red packet quantity cannot be less than %d, please re-enter."
RedPacketQuantitySet = "✅ Red packet quantity set to: %d"
TooManyAttempts = "❌ Too many attempts, please re-initiate the red packet creation."
SetUserStateError = "❌ System busy, failed to set user state, please try again later."
# Red Packet Amount Input
EnterRedPacketTotalAmount = "Please reply with the total amount(%s) you want to send? Example: 8.88"
EnterRedPacketSingleAmount = "Please reply with the amount per red packet(%s)? Example: 8.88"
InvalidAmountFormat = "❌ Invalid amount format, please enter a valid number."
AmountPrecisionError = "⚠️ The number of decimal places for the amount cannot exceed 3."
RedPacketMinAmountError = "⚠️ The amount per red packet cannot be less than %s."
InsufficientBalance = "❌ Insufficient account balance."
GetBalanceError = "❌ Failed to get account balance, please try again later."
ConvertBalanceError = "❌ Failed to convert account balance, please try again later."
RedPacketAmountSet = "✅ Red packet amount has been set."
# Red Packet Blessing Flow
EnterRedPacketBlessingPrompt = "Please add a message to your red packet:"
InputBlessingButton = "✍️ Enter Message"
SkipBlessingButton = "➡️ Use Default"
EnterBlessingContentPrompt = "Please enter the message content (up to 200 characters):"
BlessingTooLong = "❌ Input content is too long, please limit it to within 200 characters. Please re-enter:"
BlessingPreview = "Current Message:\n%s"
ConfirmButton = "✅ Confirm" # Note: This might be a general key, ensure it doesn't conflict
ReEnterButton = "🔄 Re-enter" # Note: This might be a general key, ensure it doesn't conflict
BlessingSkipped = "✅ Message skipped, ready to select cover..." # Temporary
BlessingConfirmed = "✅ Message confirmed, ready to select cover..." # Temporary
# 红包封面选择流程
ConfirmCoverTitle = "🖼️ Confirm Red Packet Cover"
RedPacketConfirmCoverButton = "✅ Confirm"
CustomCoverButton = "🖼️ Custom Cover"
CustomCoverSelectionTitle = "🖼️ Select Custom Cover"
NoCustomCovers = "❌ No custom covers approved yet"
UseThisCoverButton = "✅ Use This Cover"
PageIndicator = "%d/%d"
CoverConfirmed = "✅ Cover confirmed, creating red packet..."
NotImplementedYet = "🚧 Feature not implemented yet" # 通用占位符
# Red Packet Summary & Confirmation (New Keys)
RedPacketSummaryTitle = "🎁 Red Packet Confirmation"
RedPacketTypeLabel = "🎁 Type"
RedPacketTypeFixed = "💰 Fixed Amount" # Text representation
RedPacketTypeRandom = "🎲 Random Amount" # Text representation
TypeLabel = "Type"
TokenLabel = "Token"
RedPacketQuantityLabel = "🔢 Quantity"
RedPacketBlessingLabel = "📝 Memo"
DefaultBlessing = "🎉 May you be prosperous and have a great fortune!"
FixedAmountLabel = "💰 Single Amount"
TotalAmountLabel = "💰 Total Amount"
RedPacketCancelled = "❌ Red packet cancelled."
RedPacketCreationFailed = "❌ Red packet creation failed: %s"
RedPacketCreatedSuccess = "✅ Red packet created successfully!"
RedPacketPremiumStatus = "Premium Red Packet"
RedPacketNormalStatus = "Normal Red Packet"
SetAsPremiumRedPacket = "🌟 Set as Premium"
CancelPremiumRedPacket = "❌ Cancel Premium"
RedPacketPremiumUpdated = "✅ Red packet premium status updated"
RedPacketPremiumUpdateFailed = "❌ Failed to update red packet premium status"
RedPacketSetPremiumSuccess = "✅ Successfully set as premium red packet"
RedPacketCancelPremiumSuccess = "✅ Successfully cancelled premium status"
RedPacketPremiumRequired = "❌ Sorry, this red packet is only available for Telegram Premium members"
RedPacketNotFound = "❌ Red packet not found"
InvalidOperation = "❌ Invalid operation"
InvalidOperationOrExpired = "⏱️ Invalid operation or expired"
SystemErrorConfigMissing = "❌ System error: Configuration missing"
UnknownOperation = "❓ Unknown operation"
# Red Packet Password Verification
RedPacketEnterPasswordPrompt = "🔑 Please enter your payment password to create the red packet:\n🔑"
RedPacketCreationPending = "⏳ Red packet creation is in progress..." # 用于免密或验证成功后的 TODO 占位消息
RedPacketPasswordFailedGeneric = "❌ Payment password verification failed, please try again." # 通用验证失败提示
RedPacketPasswordCancelled = "❌ Red packet creation has been cancelled." # 取消密码输入
# 红包分享
ShareRedPacketButton = "📤 Share Red Packet"
ErrorInvalidShareQuery = "⚠️ Invalid share request."
ErrorRedPacketNotFoundOrQueryFailed = "❌ Failed to query red packet information or the red packet does not exist."
ErrorRedPacketNotFound = "⚠️ Red packet not found."
ErrorTokenInfoQueryFailed = "⚠️ Token information query failed."
ShareRedPacketInlineTitle = "Red Packet from %s"
ShareRedPacketInlineDescription = "%d red packets of %s - %s"
ShareRedPacketSentMessage = "🧧 %s sent a red packet\n💵 Total:%s %s\n💰 Remaining Amount:%s\n📦 Remaining Quantity:%d"
ShareRedPacketSentMessageMemo = "📝 Memo: %s"
ShareRedPacketPremiumOnly = "⭐ Telegram Premium members only"
"RedPacketTypeRandomDisplay" = "🎲 Random"
"RedPacketTypeFixedDisplay" = "💰 Fixed"
# 红包内联查询 V2
"RedPacketStatusLabel" = "🚦 Status"
"RedPacketRemainingQuantityLabel" = "🔢 Remaining Quantity"
"RedPacketRemainingAmountLabel" = "💰 Remaining Amount"
"RedPacketStatusActive" = "✅ Active"
"RedPacketStatusEmpty" = "🎉 Claimed"
"RedPacketStatusExpired" = "⏱️ Expired"
"RedPacketStatusCancelled" = "❌ Cancelled" # 根据 entity 定义添加
"RedPacketStatusUnknown" = "❓ Unknown (%s)" # 未知状态字符串占位符
"ShareRedPacketInlineDescriptionDetailed" = "Remaining: %d | Amount: %s %s | Status: %s"
"ShareRedPacketInlineTitleDetailed" = "Red Packet from %s of %s"

# Cover Logic Translations
"ErrorGenericFallback" = "❌ An error occurred while processing, please try again later."
"ErrorInternalRetry" = "❌ Internal error, please try again later"
"ErrorGetStateFailedRetry" = "❌ Failed to get operation status, please try again"
"ErrorStateExpiredOrInvalid" = "⏱️ Operation expired or invalid"
"ErrorGetUserInfoFailedRetry" = "❌ Failed to get user information, please try again"
"ErrorSetStateFailedRetry" = "❌ Failed to set operation status, please try again"
"ErrorGetCoverInfoFailedRetry" = "❌ Failed to get cover information, please try again"
"ErrorDeleteCoverFailed" = "❌ Failed to delete the cover"
"ErrorDeleteCoverFailedRetry" = "❌ Failed to delete the cover, please try again later"
"ErrorGetCoverImagesFailed" = "❌ Failed to get cover images"
"ErrorGetCoverListFailedRetry" = "❌ Failed to get cover list, please try again later"
"NoCoversUploadedYet" = "❌ You haven't uploaded any red packet covers yet"
"ErrorLoadCoverInfoFailedRetry" = "❌ Failed to load cover information, please try again later"
"StatusSuccess" = "🚦 Status: Success"
"ReasonNotProvided" = "❌ Not Provided"
"StatusFailWithReason" = "🚦 Status: Failed Review\n⛔ Reason: %s"
"StatusUnknownWithValue" = "🚦 Status: Unknown %s"

# Red Packet Claim Notifications
RedPacketClaimNotificationTitle = "🎉 Red Packet Claimed"
RedPacketClaimNotificationClaimer = "👤 Claimed by: %s"
RedPacketClaimNotificationAmount = "💰 Amount: %s %s"
RedPacketClaimNotificationRedPacketID = "🧧 Red Packet ID: %s"
RedPacketClaimNotificationTime = "⏰ Time: %s"
RedPacketClaimNotificationMessage = "🎉 <b>Red Packet Claimed</b>\n\n👤 Claimed by: %s\n💰 Amount: %s %s\n🧧 Red Packet ID: <code>%s</code>\n⏰ Time: %s"
"CoverPageInfo" = "🚦 Status: %s \n🖼️ Cover %d/%d"
"CoverImageRequirements" = "🖼️ Image Requirements:\n- 📏 Dimensions: 200×200 to 1000×1000 pixels\n- 🖼️ Format: JPG or PNG\n- 💾 Size: No more than 2MB\n- ⚠️ Content: Please do not upload illegal content, all images are subject to review"
"ButtonUploadCover" = "🖼️ Upload Cover"
"ButtonBack" = "◀️ Back"
"ButtonPrevious" = "<< "
"ButtonDelete" = "🗑️ Delete"
"ButtonNext" = ">> "
"ButtonConfirmDelete" = "✅ Confirm Delete"
"ButtonCancel" = "❌ Cancel"

# Ensure existing keys have correct values (add/update if necessary)
# Note: If these keys already exist with different values, this might overwrite them.
# Consider reading the file first if precise checking is needed.
RedpacketCoverUploadSuccess = "✅ Cover uploaded successfully, waiting for review."
RedpacketCoverDeleteConfirmPrompt = "⚠️ Are you sure you want to delete this cover?"
RedpacketCoverDeleteSuccessPartialRefresh = "✅ Deletion successful, but page refresh may be inaccurate"
RedpacketCoverUploadPrompt = "🖼️ Please send an image as the red packet cover."
error_cover_dimensions = "❌ Image dimensions do not meet the requirements, please make sure the image width and height are between 200 and 1000 pixels."
"ButtonSelectCover" = "✅ Select This Cover"
"NoApprovedCoversAvailable" = "❌ No approved cover images available. Please upload and wait for approval first."
"SelectCoverPageInfo" = "🖼️ Select Cover %d/%d"
"CoverSelectedSuccessfully" = "✅ Cover selected, returning to payment confirmation..."
"ErrorRedPacketRecordNotFound" = "❌ Red packet record not found, please create a new one."
"ErrorUpdateCoverFailed" = "❌ Failed to update cover, please try again."

ClaimRedPacketButton="🎁 Claim Red Packet"
RedPacketEmptyButton="❌ Fully Claimed"
RedPacketCancelledButton="❌ Cancelled"
RedPacketExpiredButton="⏱️ Expired"
RedPacketFullyClaimed="This red packet has been fully claimed!"
RedPacketCancelledMessage="This red packet has been cancelled!"
RedPacketExpiredMessage="This red packet has expired!"

# 红包领取异步流程
RedPacketClaimSubmitted = "⏳ Your red packet claim request is being processed..."
RedPacketClaimSuccessWithAmount = "🎉 Congratulations! You have received %s %s red packet!"
RedPacketNotActive = "❌ Red packet has not been activated."
RedPacketAlreadyClaimed = "❌ You have already claimed this red packet."
RedPacketEmpty = "🎉 The red packet has been claimed."
RedPacketClaimFailedSystem = "❌ System busy, failed to claim the red packet, please try again later."
RedPacketClaimRedisLockFailed = "❌ System busy, please try again later (failed to get the lock)."
KafkaPublishFailed = "❌ Failed to send the claim request, please try again later."
KafkaConsumeError = "❌ An error occurred while processing the claim request."
NotificationSendFailed = "❌ Failed to send the claim result notification."

# Red Packet History
ButtonViewClaims = "📜 View Claim Records"
ButtonBackToMenu = "🏠 Back to Menu"
ButtonBackToSent = "⬅️ Back to Red Packet Details"
SentHistoryPageTitle = "🎁 Red Packets Sent"
NoSentRedPackets = "❌ You don't have any ended red packets."
NoActiveRedPackets = "❌ You don't have any active red packets."
NoEndedRedPackets = "❌ You don't have any ended red packets."
SentInfoFormat = "🎁 Type: %s\n💰 Amount: %s %s\n💰 Remaining: %s %s\n🚦 Status: %s\n⏰ Time: %s\n📊 Progress: %d/%d"
ClaimHistoryPageTitle = "📜 Red Packet #%d Claim Records"
NoClaimsYet = "❌ No one has claimed this red packet yet."
ClaimInfoFormat = "%d. %s claimed %s %s (%s) \n"

# 红包记录功能

# 红包撤销功能
ButtonCancelRedPacket = "🚫 Cancel Red Packet"
ConfirmCancelRedPacket = "⚠️ Are you sure you want to cancel this red packet? The unclaimed amount will be refunded."
ButtonConfirmCancel = "✅ Confirm Cancellation"
ButtonCancelAction = "❌ Cancel"
RedPacketCancelledSuccess = "✅ Red packet successfully cancelled, the amount has been refunded."
RedPacketCancelledFailed = "❌ Red packet cancellation failed, reason: %s"
RedPacketCannotCancelNotActive = "❌ Only active red packets can be cancelled."
RedPacketCannotCancelClaimed = "❌ The red packet has been claimed and cannot be cancelled."
RedPacketCannotCancelFullyClaimed = "❌ The red packet has been fully claimed and cannot be cancelled."
RedPacketCannotCancelNotCreator = "❌ You are not the creator of this red packet, so you cannot cancel it."
DatabaseError = "❌ Database operation failed, please try again later."

# 收款流程
ReceiveNoTokensMessage = "❌ There are currently no tokens available for receiving payments."

# 收款功能 (Receive Payment)
ReceivePromptSelectToken = "Please select the token you want to receive:"
ReceiveButtonGetShareableLink = "🔗 Get Shareable Link (%s)"
ReceiveAlertShareLinkGenerated = "✅ %s payment link generated:\n{Link}\n\nPlease copy and share it with the payer."
ReceiveInlineResultTitle = "⬇️ Payment Request: %s %s"
ReceiveInlineResultDescription = "Payment request from %s"
ReceiveInlineResultMessageText = "%s has requested payment from you\n💰 Amount: %s %s\nPlease click the button below to pay."
ReceiveButtonPayNow = "Pay Now"
ReceiveErrorCreateRequestFailed = "❌ Failed to create payment request"
ReceiveErrorFetchRequestFailed = "❌ Failed to get payment request"
ReceiveErrorPaymentFailed = "❌ Payment failed"
ReceiveErrorAlreadyPaid = "❌ Payment request has already been paid"
ReceiveErrorExpired = "⏱️ Payment request has expired"
ReceiveErrorCancelled = "❌ Payment request has been cancelled"
ReceiveErrorNotFound = "❌ Payment request not found"
ReceiveErrorCannotPaySelf = "❌ Cannot pay yourself"


# Receive Payer Selection
receiveSelectPayerButton = "👥 Select Payer"
receivePayerSelectionPrompt = "💸 You have chosen to receive payment with %s.\n - Click the button below to select the payer."



ReceiveErrorInvalidFormat = "❌ Invalid format"
ReceiveErrorInvalidAmount = "❌ Invalid amount"
ReceiveErrorTokenNotAllowed = "❌ Token not allowed for receiving payments"
ReceiveErrorAmountOutOfRange = "❌ Amount out of range"
ReceivePromptEnterPasswordWithDetails = "🔑 Please enter your payment password to complete the payment:\n%s\n%s"
ReceiveLabelRequester= "- Requester"
ReceiveLabelAmount = "-💰 Amount"
ReceiveLabelMemo = "-📝 Memo"
ReceiveLabelStatus = "-🚦 Status"
ReceiveLabelCreatedAt = "-⏰ Created At"
ReceiveLabelExpiresAt = "-⏰ Expires At"
ReceiveLabelPaidAt = "-⏰ Paid At"
ReceiveLabelCancelledAt = "-⏰ Cancelled At"
ReceiveLabelUpdatedAt = "-⏰ Updated At"
ReceiveLabelDeletedAt = "-⏰ Deleted At"
ReceiveLabelSymbol = "-🪙 Token Symbol"
ReceiveLabelStatusPending = "-⏳ Awaiting Payment"
ReceivePromptConfirmPaymentPassFree = "Please confirm the following payment information (pass-free payment):\n\n%s\n\nClick the button below to complete the payment."
ReceivePromptConfirmPayment = "Please confirm the following payment information:\n\n%s\n\n🔑 Please enter your payment password to complete the payment."
ReceivePromptConfirmPaymentWithMemo = "Please confirm the following payment information:\n\n%s\n\n📝 Memo: %s\n\n🔑 Please enter your payment password to complete the payment."
ButtonConfirmPayment="✅ Confirm Payment"
ReceiveSuccessPaymentComplete = "✅ Payment successful!"
ReceiveSuccessPaymentCompleteWithMemo = "✅ Payment successful!\n📝 Memo: %s"
ReceiveErrorPaymentFailedWithReason = "❌ Payment failed: %s"
ReceiveErrorPaymentFailedWithReasonAndMemo = "❌ Payment failed: %s\n📝 Memo: %s"
ReceiveErrorPaymentFailedWithMemo = "❌ Payment failed\n📝 Memo: %s"
Google2FAEnterCode = "🔑 Please enter your Google verification code"
Google2FACodeInvalidRetry = "❌ Invalid verification code, please try again"
Google2FACodeInvalid = "❌ Invalid verification code"
Google2FACodeFormatError = "❌ Verification code format error"

# Transfer Verification Errors
ErrorNotTransferSender = "This operation can only be performed by the payer."
TransactionType_red_packet_claim = "Claim Red Packet"
TransactionType_red_packet_refund = "Red Packet Refund"
TransactionType_red_packet_create="Create Red Packet"

ErrorNeedSetPaymentPasswordTitle = "Please set a payment password"
ErrorNeedSetPaymentPasswordDesc = "For the security of your funds, please set a payment password before performing any operations."
ErrorCheckingSecurityStatus = "An error occurred while checking account status, please try again later."
InvalidTelegramIdFormat10Digits="❌ Invalid Telegram ID format, please enter a 10-digit ID."

# Username requirement message
PleaseSetUsernameFirst = '''🔧 <b>Username Required</b>

Hi there! To use this bot, you need to set up a Telegram username first.

<b>📝 How to set your username:</b>
1. Open Telegram Settings
2. Go to "Edit Profile"
3. Set a unique username (e.g., @yourname)
4. Save your changes
5. Come back and send /start again

<b>💡 Why do we need this?</b>
Your username helps us identify you securely and enables features like transfers and payments.

<i>Once you've set your username, restart the bot with /start to continue!</i>'''

# 转账相关消息
transferAlreadyCollected = "This transfer has already been collected"

# Transfer Verification
TransferPasswordPromptDetails = "You are making a transfer\nAmount: %s %s\n\nPlease enter your payment password:"
enterPasswordPrompt = "Please enter your payment password:"



ValidationAmountRequired = "Amount is required"
ValidationAmountTooLong = "Amount cannot be longer than 10 digits"
ValidationAmountInvalidChars = "Amount can only contain numbers"
ValidationAmountLeadingZero = "Amount cannot start with 0"
ValidationAmountTooSmall = "Amount cannot be less than 0.001"
ValidationAmountTooLarge = "Amount cannot be greater than 1000000"
ValidationAmountInvalidFormat = "Invalid amount format"
ValidationAmountZero = "Amount cannot be 0"
ValidationAmountNegative = "Amount cannot be negative"
ValidationAmountTooManyDecimals = "Amount cannot have more than %s decimal places"
ValidationAmountInvalid = "Invalid amount"
ValidationContextInvalid = "Invalid validation context"
ValidationSymbolRequired = "Token symbol is required"
ValidationAmountInvalidValue = "Invalid amount value"
ValidationAmountOverflow = "Amount value overflow"
ValidationTokenNotFound = "Token not found"
ValidationTokenInactive = "Token is inactive"
ValidationTokenInvalidDecimals = "Invalid token decimals configuration"
ValidationAmountInconsistent = "Amount value inconsistent"
# Unified Notifications (Key format: {#notification.event_name})
NotificationPaymentSent = "✅ You have successfully paid %s %s to %s."
NotificationPaymentReceived = "✅ You have received %s %s from %s."
NotificationDirectInvite = "🎉 User %s [%d] you directly invited has successfully registered!"
NotificationIndirectInvite = "🎊 User %s [%d] you indirectly invited has successfully registered!"
NotificationDepositSuccess = "✅ Congratulations! Your deposit of %s %s has been successfully credited."
NotificationTransferExpired = "⏱️ Reminder: Your transfer request of %s %s to %s has expired."
NotificationRedPacketExpired = "⏱️ Reminder: Your red packet (ID: %s) has expired. The remaining amount of %s %s has been refunded."
NotificationRedPacketClaimedFully = "🎉 Congratulations! Your red packet (ID: %s, Total: %s %s) has been fully claimed by %d people."
NotificationWithdrawalSuccess = "✅ Your withdrawal request (%s %s to address %s) has been processed successfully and should arrive soon."
NotificationWithdrawalFailed = "❌ Sorry, your withdrawal request (%s %s) failed. Reason: %s."

StartBot = "🚀 Start Bot"
GetFlow = "💰 Fund Flow"
Support = "👨‍💼 Customer Support"

# Menu Commands
DepositCommand = "💰 Deposit"
WithdrawCommand = "💸 Withdraw"
RedPacketCommand = "🧧 Red Packet"
TransferCommand = "💫 Transfer"
ProfileCommand = "👤 Profile"

# Welcome Message for Reply Keyboard
WelcomeReplyKeyboard = "Welcome! "

# Support Center Related
SupportCenterText = '''👋 Hello, %s!

🔹 If you need help, you can contact us through the following methods:

💁 Online customer service hours: 24 hours a day

Please select the service you need:'''

SupportNotConfiguredText = '''👋 Hello, %s!

🔹 Customer support is temporarily unavailable.

⚠️ The system administrator has not yet configured customer service contact information.

📞 For urgent assistance, please contact the system administrator.

🔄 Please try again later or use other features.'''

SupportLiveChatButton = "Contact Support"
SupportEmailButton = "📧 Email Support"

# Help Center Related
HelpCenterText = '''🤖 <b>Help Center</b>

<b>📋 Available Commands:</b>
/start - Start the bot and access main menu
/help - Show this help message
/support - Contact support team
/privacy - View privacy policy

<b>🚀 Main Features:</b>
💰 Wallet Management
📤 Send & Receive Transfers
🎁 Red Packets
📊 Transaction History
⚙️ Settings & Profile

<b>⚡ Quick Actions:</b>
• Use the main menu buttons for easy navigation
• Check your balance anytime
• Send money to friends instantly
• Create and share red packets

<b>🆘 Need More Help?</b>
Use /support to contact our support team directly.

<i>Last updated: 2024</i>'''

# Privacy Policy Related
PrivacyPolicyText = '''🔒 <b>Privacy Policy</b>

<b>Last Updated:</b> December 2024

<b>📋 Information We Collect:</b>
• Telegram user ID and username
• Transaction history for your account
• Wallet addresses and balances
• Communication preferences

<b>🛡️ How We Protect Your Data:</b>
• End-to-end encryption for all transactions
• Secure storage with industry-standard protocols
• Regular security audits and updates
• No sharing with third parties without consent

<b>📊 How We Use Your Information:</b>
• Process transactions and transfers
• Provide customer support
• Improve our services
• Comply with legal requirements

<b>🔐 Your Rights:</b>
• Access your personal data
• Request data correction or deletion
• Withdraw consent at any time
• Export your transaction history

<b>🍪 Cookies and Tracking:</b>
We do not use cookies or tracking technologies in this Telegram bot.

<b>📞 Data Retention:</b>
• Transaction records: 7 years (legal requirement)
• Personal data: Until account deletion
• Support conversations: 2 years

<b>🌍 International Transfers:</b>
Your data may be processed in different countries where our servers are located, always with appropriate safeguards.

<b>📧 Contact Us:</b>
For privacy-related questions: <code>%s</code>

<i>By using this bot, you agree to this privacy policy.</i>'''

# System Error Messages
"system.error.insufficientFunds" = "❌ Insufficient funds"

# Payment Request Messages
paymentRequestStatusPaid = "Already Paid"
paymentRequestStatusPending = "Pending Payment"
paymentRequestStatusExpired = "Expired"
paymentRequestStatusCancelled = "Cancelled"
paymentRequestAlreadyPaid = "This payment request has already been paid"
immediatePaymentButton = "Pay Now"
paymentRequestMessagePending = "%s has requested a payment of %s %s, please pay."
paymentRequestMessagePaid = "%s has paid %s %s to %s."
paymentRequestMessageExpired = "%s's payment request for %s %s has expired."
paymentRequestMessageCancelled = "%s's payment request for %s %s has been cancelled."
paymentRequestMessageUnknownStatus = "Payment request %s has abnormal status: %s"
paymentRequestMemoLabel = "Memo: %s"




RedPacketMaxAmountError = "❌ The amount per red packet cannot exceed %s."

# CNY Withdrawal Related
CNYWithdrawTitle = "💸 CNY Withdrawal"
CNYWithdrawAmountPrompt = "Please enter the withdrawal amount"
CNYWithdrawFeeInfo = "Fee: Amount %.2f%% + %d CNY"
CNYWithdrawFeeExample = "Example: Withdraw 100 CNY\nFee: 100 * %.2f%% + %d = %s CNY\nActual amount: 100 - %s = %s CNY"
CNYWithdrawPaymentTime = "💡 Payment time (daily): %s"
CNYWithdrawMethodSelection = "Please select withdrawal method:"
CNYWithdrawWechatQR = "💰 WeChat QR Code"
CNYWithdrawAlipayQR = "💰 Alipay QR Code"
CNYWithdrawAlipayAccount = "💰 Alipay Account"
CNYWithdrawUploadWechatQR = "Please upload your WeChat payment QR code image:"
CNYWithdrawUploadAlipayQR = "Please upload your Alipay payment QR code image:"
CNYWithdrawEnterAlipayAccount = "Please enter Alipay account and name:"
CNYWithdrawSupplementInfo = "Please enter supplementary information such as name, as QR codes sometimes require name verification!"
CNYWithdrawConfirmData = "✅ Please confirm the data"
CNYWithdrawAmount = "Withdrawal amount: %s CNY"
CNYWithdrawMethod = "Withdrawal method: %s"
CNYWithdrawAccount = "Withdrawal account: %s"
CNYWithdrawActualAmount = "Actual amount: %s CNY"
CNYWithdrawMinAmount = "Minimum withdrawal amount: %s CNY"
CNYWithdrawMaxAmount = "Maximum withdrawal amount: %s CNY"
CNYWithdrawCurrentBalance = "Current balance: %s"
CNYWithdrawUploadError = "Failed to get image, please try again"
CNYWithdrawImageSizeError = "Image size cannot exceed 2MB"
CNYWithdrawImageFormatError = "Invalid image format, please upload JPG or PNG format"
CNYWithdrawImageProcessError = "Failed to process image, please try again"
CNYWithdrawImageUploadError = "Failed to upload image, please try again"
CNYWithdrawNameLengthError = "Name length should be between 2-50 characters"
CNYWithdrawAccountLengthError = "Account information length should be between 5-100 characters"
CNYWithdrawPleaseUploadImage = "Please upload QR code image"
CNYWithdrawInputPlaceholderAmount = "100"
CNYWithdrawInputPlaceholderName = "Name"
CNYWithdrawInputPlaceholderAccount = "Alipay account Name"
CNYWithdrawEnterName = "Please enter recipient name:"
CNYWithdrawSummary = "Withdrawal Summary"
CNYWithdrawMethodLabel = "Withdrawal Method"
CNYWithdrawAccountLabel = "Alipay Account"
CNYWithdrawRecipientName = "Recipient Name: %s"

# General Withdrawal Error Messages
WithdrawSessionExpired = "❌ Withdrawal session expired, please start again"
WithdrawSessionConflict = "⚠️ Operation conflict detected, withdrawal session has expired.\n\nThis usually happens when you performed other operations while entering the password.\n\nPlease restart the withdrawal operation."
WithdrawRetryButton = "🔄 Restart Withdrawal"
WithdrawInvalidAmount = "❌ Invalid amount format"
WithdrawDisabled = "❌ Withdrawal function is disabled"
WithdrawDisabledDetail = "System maintenance, please try again later"
WithdrawTokenDisabled = "❌ This token withdrawal is suspended"
WithdrawTokenDisabledDetail = "CNY withdrawal is temporarily unavailable"
WithdrawConfigError = "❌ Configuration error"
WithdrawConfigErrorDetail = "System configuration error, please contact support"
UserNotFoundDetail = "Unable to get user information"
WalletNotFound = "❌ Wallet not found"
WalletNotFoundDetail = "Unable to get wallet information"
BalanceCheckFailed = "❌ Balance check failed"
BalanceCheckFailedDetail = "Unable to check your balance, please try again later"
SystemErrorDetail = "System processing error, please try again later"
SwapNoProductsAvailable = "❌ No available products for swap"
SwapSelectProduct = "🔄 Select trading pair to swap:"
SwapProductSelected = "✅ Selected %s"
SwapSelectDirection = "Please select trading direction:"
SwapBuy = "💰 Buy %s"
SwapSell = "💸 Sell %s"
SwapActionBuy = "buy"
SwapActionSell = "sell"
SwapEnterBuyAmount = "💸 Please enter the amount of %s you want to spend (to buy %s):"
SwapEnterSellAmount = "💰 Please enter the amount of %s you want to sell (receiving %s):"
SwapBalance = "💰 Your balance: %s %s"
SwapAgain = "🔄 Swap Again"
SwapEnterAmount = "Please enter the amount of %s to %s (for %s):"
SwapEnterPaymentPassword = "🔑 Please enter payment password to confirm swap:"
SwapExecutionError = "❌ Swap execution failed: %s"
SwapInsufficientBalance = "❌ Insufficient balance. Your available balance is %s %s."
SwapInsufficientBalanceError = "❌ Insufficient balance to complete swap"
SwapMaxAmount = "Max amount: %s %s"
SwapMaxAmountQuote = "Max payment: %s %s"
SwapMinAmount = "Min amount: %s %s"
SwapMinAmountQuote = "Min payment: %s %s"
SwapOrderDetails = "📋 Order Details\n\nOrder ID: %s\nCreated: %s\nStatus: %s\nTrade Type: %s\n\nFrom: %s %s\nTo: %s %s\nRate: %s\nFee: %s %s"
SwapOrderError = "❌ Failed to create order: %s"
SwapQuoteDetails = "📊 Swap Quote Details\n\n💱 Swap: %s %s\n📈 Estimated receive: %s %s\n💹 Current Price: 1 %s = %s %s\n💰 Estimated Fee: %s %s"
SwapQuoteError = "❌ Failed to get quote: %s"
SwapAmountBelowMinimum = "❌ Amount too small\n\nMinimum amount: %s %s\nPlease enter an amount greater than or equal to the minimum"
SwapAmountBelowMinimumQuote = "❌ Amount too small\n\nMinimum payment: %s %s\nPlease enter an amount greater than or equal to the minimum"
SwapAmountExceedsMaximum = "❌ Amount too large\n\nMaximum amount: %s %s\nPlease enter an amount less than or equal to the maximum"
SwapAmountExceedsMaximumQuote = "❌ Amount too large\n\nMaximum payment: %s %s\nPlease enter an amount less than or equal to the maximum"
SwapQuoteExpired = "⏱️ Quote has expired"
SwapQuoteExpiredHint = "Quotes are valid for 2 minutes. Your quote has timed out. Please click below to get a fresh quote."
SwapSpread = "Spread: %s%"
SwapPriceReminder = "💡 Note: Final price will be based on current market rate"
SwapSuccess = "✅ Swap successful!\n\nSwapped: %s %s\nReceived: %s %s\nRate: %s\nFee: %s %s\nOrder ID: %s"
actualPrice = "Actual Price"
cancel = "❌ Cancel"
completedAt = "Completed At"
confirm = "✅ Confirm"
failureReason = "Failure Reason"
featureNotImplemented = "🚧 Feature not implemented"
incorrectPassword = "❌ Incorrect password"
invalidAmount = "❌ Invalid amount"
invalidDirection = "❌ Invalid trading direction"
invalidPasswordFormat = "❌ Invalid password format, please enter 6 digits"
invalidProduct = "❌ Invalid product"
invalidRequest = "❌ Invalid request"
mainMenu = "🏠 Main Menu"
newSwap = "🔄 New Swap"
orderNotFound = "❌ Order not found"
orderStatusCancelled = "Cancelled"
orderStatusCompleted = "Completed"
orderStatusFailed = "Failed"
orderStatusPending = "Pending"
orderStatusProcessing = "Processing"
SwapTradeTypeBuy = "Buy"
SwapTradeTypeSell = "Sell"

# Swap Error Messages (Specific)
SwapErrorInsufficientBalance = "❌ Insufficient balance to complete swap"
SwapErrorQuoteExpired = "⏱️ Quote has expired, please get a new quote"
SwapErrorPriceSlippage = "📈 Price slippage too high, please try again"
SwapErrorAmountTooSmall = "❌ Swap amount too small, please enter a larger amount"
SwapErrorAmountTooLarge = "❌ Swap amount too large, please enter a smaller amount"
SwapErrorAmountBelowMinimum = "❌ Amount below minimum limit (%s %s)"
SwapErrorAmountExceedsMaximum = "❌ Amount exceeds maximum limit (%s %s)"
SwapErrorServiceUnavailable = "🔧 Swap service temporarily unavailable, please try again later"
SwapErrorRateLimitExceeded = "⏳ Too many requests, please try again later"
SwapErrorUserLimitExceeded = "📊 You have reached your swap limit"
SwapErrorDailyLimitExceeded = "❌ Daily swap limit exceeded ($%s). Please try again tomorrow or use a smaller amount."
SwapErrorPriceManipulation = "⚠️ Abnormal price detected, transaction blocked"
SwapErrorAccountSuspended = "🔒 Your account is temporarily unable to use swap features"
SwapErrorSecurityCheckFailed = "🛡️ Security check failed, please contact support"
SwapErrorInternalError = "❌ System error, please try again later"
SwapErrorBuyingNotAllowed = "❌ Buying is not allowed for this trading pair"
SwapErrorSellingNotAllowed = "❌ Selling is not allowed for this trading pair"
SwapErrorTradingPairNotActive = "❌ This trading pair is temporarily unavailable"
SwapErrorTradingPairNotFound = "❌ Trading pair not supported"
SwapErrorInvalidConfiguration = "❌ Invalid trading configuration, please contact support"
order = "Order %s"
processingSwap = "⏳ Processing swap..."
productNotFound = "❌ Product not found"
serviceUnavailable = "❌ Service temporarily unavailable"
sessionExpired = "⏱️ Session expired"
transactionHash = "Transaction Hash: %s"
useTextInput = "📝 Use text input"
viewDetails = "📋 View Details"

# Swap specific messages
SwapCancelled = "❌ Swap cancelled."
SwapSessionConflict = "⚠️ Operation conflict detected, swap session has expired.\n\nThis usually happens when you performed other operations while entering the password.\n\nPlease restart the swap operation."
SwapRetryButton = "🔄 Restart Swap"
SwapHistory = "🔄 Swap History"
SwapHistoryTitle = "📊 Swap History"
SwapHistoryEmpty = "No swap records yet."
SwapHistoryStartSwap = "🔄 Start Swap"
SwapHistoryBackToProfile = "👤 Back to Profile"
SwapOrderBack = "⬅️ Back"
SwapOrderMainMenu = "🏠 Main Menu"
SwapHistoryOrderStatus = "Status"
SwapHistoryFrom = "From"
SwapHistoryTo = "To"
SwapHistoryTime = "Time"
SwapHistoryViewDetails = "📋 View Details"
SwapHistoryPage = "Page %d / %d"
SwapHistoryCurrentPage = "Page %d"
SwapHistoryPreviousPage = "⬅️ Previous"
SwapHistoryNextPage = "➡️ Next"
SwapCurrentPrice = "💹 Current price: 1 %s = %s %s"
SwapCNYBuyPrice = "💹 Buy price: 1 %s = %s %s"
SwapCNYSellPrice = "💹 Sell price: 1 %s = %s %s"
SwapCNYReverseBuyPrice = "💱 Reference: 1 %s = %s %s"
SwapCNYReverseSellPrice = "💱 Reference: 1 %s = %s %s"
SwapPriceUnavailable = "💹 Price: Market rate"
NoRedPacketCover= "❌ No Setup red packet cover."
RedPacketNoCoverNotice="❌ No red packet cover"
SingleAmountLabel = "Single Amount"
paymentAmountExceedsMaximum = "❌ Payment amount exceeds maximum. Maximum amount: %s %s"

[Swap]
SwapBuyingNotAllowed = "Buying is not allowed for this trading pair"
SwapSellingNotAllowed = "Selling is not allowed for this trading pair"

# Withdraw Error Messages
WithdrawSelectSymbolOrChainFirst = "❌ Please select symbol or chain first"

# Rate Limit Messages
rateLimitExceeded = "⚠️ You are sending messages too frequently. Please wait 60 seconds before trying again."
rateLimitExceededCallback = "Too many requests. Please wait."
rateLimitCountdown = "⏳ You are temporarily blocked. Please wait %d seconds before trying again."

# Commission Notification Messages
CommissionNotificationDirect = "💰 *Commission Received*\n\nCongratulations! Your direct referral {.Username}'s gaming bets have earned you *{.Amount} {.Symbol}* commission!\n\n💎 Type: Direct Referral\n📈 Rate: {.Rate}%\n⏰ Time: {.Time}"
CommissionNotificationIndirect = "💰 *Commission Received*\n\nCongratulations! Your indirect referral's gaming bets have earned you *{.Amount} {.Symbol}* commission!\n\n💎 Type: Indirect Referral\n📈 Rate: {.Rate}%\n⏰ Time: {.Time}"
CommissionSummaryDaily = "📊 *Daily Commission Summary*\n\nCommissions for {.Date} have been settled:\n\n💰 Total: *{.TotalAmount} {.Symbol}*\n👥 Direct: {.DirectAmount} {.Symbol}\n🔗 Indirect: {.IndirectAmount} {.Symbol}\n📝 Transactions: {.Count}\n\nThank you for your promotion efforts! Keep it up! 🚀"

# Admin Personal Stats
AdminPersonalStatsPlaceholder = "Personal statistics feature under development..."
AdminPersonalStatsNoData = "No personal statistics data available"
AdminPersonalStatsFormat = "👤 Personal Statistics\n\nUser ID: %d\nTotal Amount: %s"
AdminPersonalStatsHeader = "Personal Statistics Query"
AdminPersonalStatsSearchInstructions = "Please enter the user's Telegram ID or username to query"
AdminPersonalStatsSearchExamples = "Query Examples"
AdminPersonalStatsSearchByTelegramID = "Query by Telegram ID"
AdminPersonalStatsSearchByUsername = "Query by Username"
AdminPersonalStatsUserNotFound = "❌ User not found"
AdminPersonalStatsError = "❌ Query failed, please try again later"
AdminPermissionError = "❌ Permission check failed"
AdminPermissionDenied = "❌ Insufficient permissions"
AdminSystemError = "❌ System error"

# Admin Input Validation Messages
AdminFormatError = "❌ Invalid input format"
AdminBalanceFormatErrorStrict = "❌ Invalid input format\n\nCorrect format:\nUserID Amount\n@Username Amount\nUserID Amount bonus\n@Username Amount bonus\n\nExamples:\n7675700937 100\n@agoukuaile 100\n7675700937 100 bonus"
AdminInvalidOperationType = "❌ Invalid operation type\n\nSupported operation types:\n• Empty (regular balance adjustment)\n• bonus (bonus flow adjustment)\n\nExamples:\n7675700937 100\n7675700937 100 bonus"
AdminUserNotFoundError = "❌ User not found"
AdminInsufficientBalanceError = "❌ Operation failed: Insufficient wallet balance\n\n%s's wallet balance: %s CNY"
AdminRetryInputPrompt = "💡 Please re-enter the correct format, or click the button below to return to admin center"
AdminPersonalStatsBanSuccess = "✅ User has been banned"
AdminPersonalStatsUnbanSuccess = "✅ User has been unbanned"

# Personal Stats Detail Fields
AdminPersonalStatsDetailHeader = "Personal Detailed Statistics"
AdminUserInfo = "User Information"
AdminUserID = "User ID"
AdminTelegramID = "Telegram ID"
AdminUsername = "Username"
AdminUserStatus = "User Status"
AdminUserStatusActive = "Active"
AdminUserStatusBanned = "Banned"
AdminBalanceInfo = "Balance Information"
AdminWalletBalance = "Wallet Balance"
AdminGameBalance = "Game Balance"
AdminTotalBalance = "Total Balance"
AdminFinancialInfo = "Financial Information"
AdminTotalDeposits = "Total Deposits"
AdminStatsTotalDeposits = "Total Deposits"
AdminTotalWithdrawals = "Total Withdrawals"
AdminNetProfitLoss = "Net Profit/Loss"
AdminActivityInfo = "Activity Information"
AdminRegisteredAt = "Registered At"
AdminLastActiveAt = "Last Active"
AdminNever = "Never"

# Flow Stats
AdminFlowStatsHeader = "Flow Statistics"
AdminFlowSummary = "Flow Summary"
AdminTotalIn = "Total In"
AdminTotalOut = "Total Out"
AdminFlowPeriodToday = "Today"
AdminFlowPeriodYesterday = "Yesterday"
AdminFlowPeriodWeek = "This Week"
AdminFlowPeriodMonth = "This Month"

# Button Labels
AdminButtonFlowToday = "Today's Flow"
AdminButtonFlowYesterday = "Yesterday's Flow"
AdminButtonFlowWeek = "This Week's Flow"
AdminButtonFlowMonth = "This Month's Flow"
AdminButtonBanUser = "Ban User"
AdminButtonUnbanUser = "Unban User"
AdminButtonBackToSearch = "Back to Search"

# Red Packet Additional Conditions
"(仅限 Premium会员领取)" = "(Premium Members Only)"
"(指定群组条件红包)" = "(Group-Specific Red Packet)"
"流水要求" = "Betting Volume Requirement"
"(指定群组流水)" = "(Group-Specific Volume)"
"流水时间" = "Volume Period"
"流水金额" = "Volume Amount"
"天" = "days"
"(%d天)" = "(%d days)"
"(需要验证码)" = "(Verification Code Required)"
"今日" = "Today"
"近7天" = "Last 7 days"
"近30天" = "Last 30 days"
"本月" = "This month"
"总流水" = "Total volume"
"近%d天" = "Last %d days"
"需要%s流水达到%s %s" = "Requires %s volume to reach %s %s"
