package admin

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	tgbotapi "github.com/a19ba14d/telegram-bot-api/v5"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"

	"telegram-bot-api/internal/model"
	"telegram-bot-api/internal/model/callback"
	"telegram-bot-api/internal/service"
)

// handlePersonalStatistics handles the personal statistics menu callback
func handlePersonalStatistics(ctx context.Context, callbackQuery *tgbotapi.CallbackQuery) (callback.CallbackResponse, error) {
	isAdmin, err := service.Admin().IsCurrentUserAdmin(ctx)
	if err != nil {
		return callback.NewAnswerCallback(callbackQuery.ID, service.I18n().T(ctx, "AdminPermissionError")), nil
	}
	if !isAdmin {
		return callback.NewAnswerCallback(callbackQuery.ID, service.I18n().T(ctx, "AdminPermissionDenied")), nil
	}

	// Set user state to expect personal stats search input
	userState := &model.UserState{
		State: "admin_personal_stats_search",
		Context: map[string]string{
			"message_id": fmt.Sprintf("%d", callbackQuery.Message.MessageID),
			"chat_id":    fmt.Sprintf("%d", callbackQuery.Message.Chat.ID),
		},
		ExpectedInputType: "text",
		RetryCount:        0,
		TTL:               600, // 10 minutes
	}

	err = service.UserState().SetUserStateByTelegramId(ctx, callbackQuery.From.ID, userState)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to set user state: %v", err)
		return callback.NewAnswerCallback(callbackQuery.ID, service.I18n().T(ctx, "AdminSystemError")), nil
	}

	// Show search instruction
	text := BuildPersonalStatsSearchMessage(ctx)
	keyboard := BuildPersonalStatsKeyboard(ctx)

	return &callback.EditMessageResponse{
		CallbackQueryID: callbackQuery.ID,
		ChatID:          callbackQuery.Message.Chat.ID,
		MessageID:       callbackQuery.Message.MessageID,
		Text:            text,
		ParseMode:       "HTML",
		InlineKeyboard:  &keyboard,
	}, nil
}

// handlePersonalStatsDetail handles displaying detailed personal statistics
func handlePersonalStatsDetail(ctx context.Context, callbackQuery *tgbotapi.CallbackQuery, userIDStr string) (callback.CallbackResponse, error) {
	isAdmin, err := service.Admin().IsCurrentUserAdmin(ctx)
	if err != nil {
		return callback.NewAnswerCallback(callbackQuery.ID, service.I18n().T(ctx, "AdminPermissionError")), nil
	}
	if !isAdmin {
		return callback.NewAnswerCallback(callbackQuery.ID, service.I18n().T(ctx, "AdminPermissionDenied")), nil
	}

	// Parse user ID
	userID, err := strconv.ParseUint(userIDStr, 10, 64)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to parse user ID: %v", err)
		return callback.NewAnswerCallback(callbackQuery.ID, service.I18n().T(ctx, "AdminSystemError")), nil
	}

	var stats *service.UserDetailedStats
	// Get detailed statistics
	stats, err = service.PersonalStatsService().GetUserDetailedStats(ctx, userID)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to get user detailed stats: %v", err)
		return callback.NewAnswerCallback(callbackQuery.ID, service.I18n().T(ctx, "AdminPersonalStatsError")), nil
	}

	// Build message
	text := BuildUserDetailedStatsMessage(ctx, stats)
	keyboard := BuildUserStatsKeyboard(ctx, userID, stats.Status)

	return &callback.EditMessageResponse{
		CallbackQueryID: callbackQuery.ID,
		ChatID:          callbackQuery.Message.Chat.ID,
		MessageID:       callbackQuery.Message.MessageID,
		Text:            text,
		ParseMode:       "HTML",
		InlineKeyboard:  &keyboard,
	}, nil
}

// handlePersonalStatsFlowRange handles flow statistics by date range
func handlePersonalStatsFlowRange(ctx context.Context, callbackQuery *tgbotapi.CallbackQuery, params string) (callback.CallbackResponse, error) {
	isAdmin, err := service.Admin().IsCurrentUserAdmin(ctx)
	if err != nil {
		return callback.NewAnswerCallback(callbackQuery.ID, service.I18n().T(ctx, "AdminPermissionError")), nil
	}
	if !isAdmin {
		return callback.NewAnswerCallback(callbackQuery.ID, service.I18n().T(ctx, "AdminPermissionDenied")), nil
	}

	// Parse params: userID:range
	parts := strings.Split(params, ":")
	if len(parts) != 2 {
		return callback.NewAnswerCallback(callbackQuery.ID, service.I18n().T(ctx, "AdminSystemError")), nil
	}

	userID, err := strconv.ParseUint(parts[0], 10, 64)
	if err != nil {
		return callback.NewAnswerCallback(callbackQuery.ID, service.I18n().T(ctx, "AdminSystemError")), nil
	}

	// Get date range
	var startDate, endDate *gtime.Time
	timeHelper := service.NewStatisticsTimeHelper("Asia/Shanghai")

	switch parts[1] {
	case "today":
		period := timeHelper.GetPeriodByName("today")
		startDate = period.StartTime
		endDate = period.EndTime
	case "yesterday":
		period := timeHelper.GetPeriodByName("yesterday")
		startDate = period.StartTime
		endDate = period.EndTime
	case "week":
		period := timeHelper.GetPeriodByName("this_week")
		startDate = period.StartTime
		endDate = period.EndTime
	case "month":
		period := timeHelper.GetPeriodByName("this_month")
		startDate = period.StartTime
		endDate = period.EndTime
	default:
		return callback.NewAnswerCallback(callbackQuery.ID, service.I18n().T(ctx, "AdminSystemError")), nil
	}

	// Get flow statistics
	flowStats, err := service.PersonalStatsService().GetUserFlowByDateRange(ctx, userID, startDate, endDate)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to get user flow stats: %v", err)
		return callback.NewAnswerCallback(callbackQuery.ID, service.I18n().T(ctx, "AdminPersonalStatsError")), nil
	}

	// Build message
	text := BuildUserFlowStatsMessage(ctx, flowStats, parts[1])
	keyboard := BuildFlowStatsKeyboard(ctx, userID)

	return &callback.EditMessageResponse{
		CallbackQueryID: callbackQuery.ID,
		ChatID:          callbackQuery.Message.Chat.ID,
		MessageID:       callbackQuery.Message.MessageID,
		Text:            text,
		ParseMode:       "HTML",
		InlineKeyboard:  &keyboard,
	}, nil
}

// handlePersonalStatsBan handles user ban/unban actions
func handlePersonalStatsBan(ctx context.Context, callbackQuery *tgbotapi.CallbackQuery, params string) (callback.CallbackResponse, error) {
	isAdmin, err := service.Admin().IsCurrentUserAdmin(ctx)
	if err != nil {
		return callback.NewAnswerCallback(callbackQuery.ID, service.I18n().T(ctx, "AdminPermissionError")), nil
	}
	if !isAdmin {
		return callback.NewAnswerCallback(callbackQuery.ID, service.I18n().T(ctx, "AdminPermissionDenied")), nil
	}

	// Parse params: userID:action
	parts := strings.Split(params, ":")
	if len(parts) != 2 {
		return callback.NewAnswerCallback(callbackQuery.ID, service.I18n().T(ctx, "AdminSystemError")), nil
	}

	userID, err := strconv.ParseUint(parts[0], 10, 64)
	if err != nil {
		return callback.NewAnswerCallback(callbackQuery.ID, service.I18n().T(ctx, "AdminSystemError")), nil
	}

	// Get display name before updating status
	displayName, err := service.User().GetUserDisplayName(ctx, userID)
	if err != nil {
		displayName = fmt.Sprintf("User_%d", userID)
	}

	// Update user status
	var newStatus int
	var reason string
	var successMsg string

	switch parts[1] {
	case "ban":
		newStatus = 0 // banned
		reason = "Banned by admin via personal statistics"
		successMsg = fmt.Sprintf("%s 已封号", displayName)
	case "unban":
		newStatus = 1 // active
		reason = "Unbanned by admin via personal statistics"
		successMsg = fmt.Sprintf("%s 已解封", displayName)
	default:
		return callback.NewAnswerCallback(callbackQuery.ID, service.I18n().T(ctx, "AdminSystemError")), nil
	}

	err = service.PersonalStatsService().UpdateUserStatus(ctx, userID, newStatus, reason)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to update user status: %v", err)
		return callback.NewAnswerCallback(callbackQuery.ID, service.I18n().T(ctx, "AdminPersonalStatsError")), nil
	}

	// Get updated statistics to refresh the message
	stats, err := service.PersonalStatsService().GetUserDetailedStats(ctx, userID)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to get updated user stats: %v", err)
		// Still show success alert even if refresh fails
		return &callback.AlertResponse{
			CallbackQueryID: callbackQuery.ID,
			Text:            successMsg,
			ShowAlert:       true,
			ChatID:          callbackQuery.Message.Chat.ID,
		}, nil
	}

	// Build updated message
	text := BuildUserDetailedStatsMessage(ctx, stats)
	keyboard := BuildUserStatsKeyboard(ctx, userID, stats.Status)

	// Return edit message response with callback query text and alert
	return &callback.EditMessageResponse{
		CallbackQueryID:   callbackQuery.ID,
		ChatID:            callbackQuery.Message.Chat.ID,
		MessageID:         callbackQuery.Message.MessageID,
		Text:              text,
		ParseMode:         "HTML",
		InlineKeyboard:    &keyboard,
		CallbackQueryText: successMsg,
		ShowAlert:         true,
	}, nil
}

// handlePersonalStatsV2 handles the V2 version of personal statistics with comprehensive data
func handlePersonalStatsV2(ctx context.Context, callbackQuery *tgbotapi.CallbackQuery, userIDStr string) (callback.CallbackResponse, error) {
	isAdmin, err := service.Admin().IsCurrentUserAdmin(ctx)
	if err != nil {
		return callback.NewAnswerCallback(callbackQuery.ID, service.I18n().T(ctx, "AdminPermissionError")), nil
	}
	if !isAdmin {
		return callback.NewAnswerCallback(callbackQuery.ID, service.I18n().T(ctx, "AdminPermissionDenied")), nil
	}

	// Parse user ID
	userID, err := strconv.ParseUint(userIDStr, 10, 64)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to parse user ID: %v", err)
		return callback.NewAnswerCallback(callbackQuery.ID, service.I18n().T(ctx, "AdminSystemError")), nil
	}

	// Get comprehensive user statistics
	stats, err := service.PersonalStatsService().GetUserDetailedStats(ctx, userID)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to get user detailed stats: %v", err)
		return callback.NewAnswerCallback(callbackQuery.ID, service.I18n().T(ctx, "AdminPersonalStatsError")), nil
	}

	// Build comprehensive statistics message
	text := BuildUserDetailedStatsV2Message(ctx, stats)
	keyboard := BuildUserStatsV2Keyboard(ctx, userID, stats.Status)

	return &callback.EditMessageResponse{
		CallbackQueryID: callbackQuery.ID,
		ChatID:          callbackQuery.Message.Chat.ID,
		MessageID:       callbackQuery.Message.MessageID,
		Text:            text,
		ParseMode:       "HTML",
		InlineKeyboard:  &keyboard,
	}, nil
}

// BuildPersonalStatsSearchMessage builds the search instruction message
func BuildPersonalStatsSearchMessage(ctx context.Context) string {
	header := fmt.Sprintf("🔍 <b>%s</b>\n\n", service.I18n().T(ctx, "AdminPersonalStatsHeader"))

	msg := header
	msg += service.I18n().T(ctx, "AdminPersonalStatsSearchInstructions") + "\n\n"
	msg += "📝 " + service.I18n().T(ctx, "AdminPersonalStatsSearchExamples") + "\n"
	msg += "• <code>123456789</code> - " + service.I18n().T(ctx, "AdminPersonalStatsSearchByTelegramID") + "\n"
	msg += "• <code>@username</code> - " + service.I18n().T(ctx, "AdminPersonalStatsSearchByUsername") + "\n"

	return msg
}

// BuildUserDetailedStatsMessage builds the detailed statistics message
func BuildUserDetailedStatsMessage(ctx context.Context, stats *service.UserDetailedStats) string {
	if stats == nil {
		return service.I18n().T(ctx, "AdminPersonalStatsNoData")
	}

	var msg strings.Builder

	// Header with username
	username := stats.Username
	if stats.FirstName != "" {
		username = stats.FirstName
	}
	msg.WriteString("📊 <b>个人详细统计</b>\n\n")
	msg.WriteString(fmt.Sprintf("%s\n", username))
	msg.WriteString(fmt.Sprintf("平台ID：%d\n", stats.UserID))
	msg.WriteString(fmt.Sprintf("TelegramID：%d\n\n", stats.TelegramID))

	// Daily deposit/withdrawal statistics
	msg.WriteString(fmt.Sprintf("今日存款：%s\n", stats.TodayDeposits.StringFixed(2)))
	msg.WriteString(fmt.Sprintf("今日取款：%s\n\n", stats.TodayWithdrawals.StringFixed(2)))

	msg.WriteString(fmt.Sprintf("昨日存款：%s\n", stats.YesterdayDeposits.StringFixed(2)))
	msg.WriteString(fmt.Sprintf("昨日取款：%s\n\n", stats.YesterdayWithdrawals.StringFixed(2)))

	// Monthly deposit/withdrawal statistics
	msg.WriteString(fmt.Sprintf("本月存款：%s\n", stats.MonthDeposits.StringFixed(2)))
	msg.WriteString(fmt.Sprintf("本月取款：%s\n\n", stats.MonthWithdrawals.StringFixed(2)))

	msg.WriteString(fmt.Sprintf("上月存款：%s\n", stats.LastMonthDeposits.StringFixed(2)))
	msg.WriteString(fmt.Sprintf("上月取款：%s\n\n", stats.LastMonthWithdrawals.StringFixed(2)))

	// Total statistics
	msg.WriteString(fmt.Sprintf("总存款：%s\n", stats.TotalDeposits.StringFixed(2)))
	msg.WriteString(fmt.Sprintf("总取款：%s\n", stats.TotalWithdrawals.StringFixed(2)))

	// Balance details
	msg.WriteString(fmt.Sprintf("💳本地钱包余额：%s\n", stats.LocalWalletBalance.StringFixed(2)))

	// Game balance details
	if len(stats.GameBalanceDetails) > 0 {
		for _, gameBalance := range stats.GameBalanceDetails {
			// Remove emoji from game name if it already has one
			gameName := gameBalance.GameName
			if strings.HasPrefix(gameName, "🎮") {
				gameName = strings.TrimPrefix(gameName, "🎮 ")
			}
			msg.WriteString(fmt.Sprintf("🎮%s余额：%s\n", gameName, gameBalance.Balance.StringFixed(2)))
		}
	}

	msg.WriteString(fmt.Sprintf("💰总余额：%s\n\n", stats.TotalBalance.StringFixed(2)))

	// Today's game provider flow statistics
	if len(stats.TodayProviderStats) > 0 {
		for _, provider := range stats.TodayProviderStats {
			msg.WriteString(fmt.Sprintf("🎮今日%s流水：%s\n", provider.ProviderName, provider.TodayPnL.StringFixed(2)))
		}
		msg.WriteString("\n")
	}

	// Yesterday's game provider flow statistics
	if len(stats.YesterdayProviderStats) > 0 {
		for _, provider := range stats.YesterdayProviderStats {
			msg.WriteString(fmt.Sprintf("🎮昨日%s流水：%s\n", provider.ProviderName, provider.TodayPnL.StringFixed(2)))
		}
		msg.WriteString("\n")
	}

	// Today's profit/loss
	msg.WriteString(fmt.Sprintf("💰今日盈亏：%s\n", stats.TodayGameProfitLoss.StringFixed(2)))

	// Calculate and display total profit/loss (total deposits - total withdrawals)
	totalProfitLoss := stats.TotalDeposits.Sub(stats.TotalWithdrawals)
	msg.WriteString(fmt.Sprintf("📈总盈亏：%s\n", totalProfitLoss.StringFixed(2)))

	// Remaining flow requirements
	msg.WriteString(fmt.Sprintf("📊剩余流水要求：%s\n\n", stats.WithdrawBettingVolume.StringFixed(2)))

	// Footer
	msg.WriteString("💡所有单位为CNY\n")

	// User status with emoji
	if stats.Status == 0 {
		msg.WriteString("用户状态：🚫 已封号")
	} else {
		msg.WriteString("用户状态：✅ 正常")
	}

	return msg.String()
}

// BuildUserDetailedStatsV2Message builds the comprehensive user statistics message (V2 version)
func BuildUserDetailedStatsV2Message(ctx context.Context, stats *service.UserDetailedStats) string {
	if stats == nil {
		return service.I18n().T(ctx, "AdminPersonalStatsNoData")
	}

	var msg strings.Builder

	// Header
	msg.WriteString(fmt.Sprintf("📊 <b>%s的总数据</b>\n\n", stats.Username))

	// User basic info
	msg.WriteString(fmt.Sprintf("用户ID：%d\n", stats.UserID))
	msg.WriteString(fmt.Sprintf("TelegramID：%d\n", stats.TelegramID))

	// Status
	statusText := "正常"
	if stats.Status == 0 {
		statusText = "已禁用"
	}
	msg.WriteString(fmt.Sprintf("用户状态：%s\n\n", statusText))

	// Balance information
	msg.WriteString(fmt.Sprintf("钱包余额：%s %s\n", stats.WalletBalance.StringFixed(2), stats.Currency))

	// Game balance
	totalGameBalance := "0.00"
	if len(stats.GameBalances) > 0 {
		totalGameBalance = stats.GameBalances[0].Balance.StringFixed(2)
	}
	msg.WriteString(fmt.Sprintf("游戏余额：%s %s\n", totalGameBalance, stats.Currency))
	msg.WriteString(fmt.Sprintf("总余额：%s %s\n\n", stats.TotalBalance.StringFixed(2), stats.Currency))

	// Financial statistics
	msg.WriteString(fmt.Sprintf("总充值：%s %s (%d次)\n", stats.TotalDeposits.StringFixed(2), stats.Currency, stats.DepositCount))
	msg.WriteString(fmt.Sprintf("总提现：%s %s (%d次)\n", stats.TotalWithdrawals.StringFixed(2), stats.Currency, stats.WithdrawalCount))
	msg.WriteString(fmt.Sprintf("总投注：%s %s (%d次)\n", stats.TotalBets.StringFixed(2), stats.Currency, stats.BetCount))

	// Net profit/loss with color indicator
	netPLPrefix := "🟢"
	if stats.NetProfitLoss.IsNegative() {
		netPLPrefix = "🔴"
	}
	msg.WriteString(fmt.Sprintf("%s 净盈亏：%s %s\n\n", netPLPrefix, stats.NetProfitLoss.StringFixed(2), stats.Currency))

	// Activity information
	registeredAt := "未知"
	if stats.RegisteredAt != nil {
		registeredAt = stats.RegisteredAt.String()
	}
	msg.WriteString(fmt.Sprintf("注册时间：%s\n", registeredAt))

	lastActiveAt := "从未"
	if stats.LastActiveAt != nil {
		lastActiveAt = stats.LastActiveAt.String()
	}
	msg.WriteString(fmt.Sprintf("最后活跃：%s\n\n", lastActiveAt))

	// Footer
	msg.WriteString("💡所有单位为CNY")
	msg.WriteString(fmt.Sprintf("\n更新时间: %s", time.Now().String()))

	return msg.String()
}

// handleBettingVolumeQuery handles the betting volume query callback
func handleBettingVolumeQuery(ctx context.Context, callbackQuery *tgbotapi.CallbackQuery, userIDStr string) (callback.CallbackResponse, error) {
	isAdmin, err := service.Admin().IsCurrentUserAdmin(ctx)
	if err != nil {
		return callback.NewAnswerCallback(callbackQuery.ID, service.I18n().T(ctx, "AdminPermissionError")), nil
	}
	if !isAdmin {
		return callback.NewAnswerCallback(callbackQuery.ID, service.I18n().T(ctx, "AdminPermissionDenied")), nil
	}

	// Parse user ID
	userID, err := strconv.ParseUint(userIDStr, 10, 64)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to parse user ID: %v", err)
		return callback.NewAnswerCallback(callbackQuery.ID, service.I18n().T(ctx, "AdminSystemError")), nil
	}

	// Set user state to expect date range input
	userState := &model.UserState{
		State: "admin_betting_volume_date_input",
		Context: map[string]string{
			"message_id": fmt.Sprintf("%d", callbackQuery.Message.MessageID),
			"chat_id":    fmt.Sprintf("%d", callbackQuery.Message.Chat.ID),
			"user_id":    fmt.Sprintf("%d", userID),
		},
		ExpectedInputType: "text",
		RetryCount:        0,
		TTL:               600, // 10 minutes
	}

	err = service.UserState().SetUserStateByTelegramId(ctx, callbackQuery.From.ID, userState)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to set user state: %v", err)
		return callback.NewAnswerCallback(callbackQuery.ID, service.I18n().T(ctx, "AdminSystemError")), nil
	}

	// Show date range input instruction
	text := BuildBettingVolumeQueryMessage(ctx)
	keyboard := BuildBettingVolumeQueryKeyboard(ctx, userID)

	return &callback.EditMessageResponse{
		CallbackQueryID: callbackQuery.ID,
		ChatID:          callbackQuery.Message.Chat.ID,
		MessageID:       callbackQuery.Message.MessageID,
		Text:            text,
		ParseMode:       "HTML",
		InlineKeyboard:  &keyboard,
	}, nil
}

// BuildUserFlowStatsMessage builds the flow statistics message
func BuildUserFlowStatsMessage(ctx context.Context, stats *service.UserFlowStats, period string) string {
	periodText := service.I18n().T(ctx, "AdminFlowPeriod"+strings.Title(period))
	header := fmt.Sprintf("📊 <b>%s - %s</b>\n\n", service.I18n().T(ctx, "AdminFlowStatsHeader"), periodText)

	msg := header

	// Summary
	msg += fmt.Sprintf("📈 <b>%s</b>\n", service.I18n().T(ctx, "AdminFlowSummary"))
	msg += fmt.Sprintf("├ %s: %s CNY\n", service.I18n().T(ctx, "AdminTotalIn"), stats.TotalIn.StringFixed(2))
	msg += fmt.Sprintf("├ %s: %s CNY\n", service.I18n().T(ctx, "AdminTotalOut"), stats.TotalOut.StringFixed(2))

	// Net flow with color
	netFlowPrefix := "🟢"
	if stats.NetFlow.IsNegative() {
		netFlowPrefix = "🔴"
	}
	msg += fmt.Sprintf("└ %s %s: %s CNY\n\n", netFlowPrefix, service.I18n().T(ctx, "AdminNetFlow"), stats.NetFlow.StringFixed(2))

	// Daily breakdown (show last 7 days max)
	if len(stats.DailyFlows) > 0 {
		msg += fmt.Sprintf("📅 <b>%s</b>\n", service.I18n().T(ctx, "AdminDailyBreakdown"))

		startIdx := 0
		if len(stats.DailyFlows) > 7 {
			startIdx = len(stats.DailyFlows) - 7
		}

		for i := startIdx; i < len(stats.DailyFlows); i++ {
			flow := stats.DailyFlows[i]
			msg += fmt.Sprintf("\n<b>%s</b>\n", flow.Date)

			if !flow.Deposits.IsZero() {
				msg += fmt.Sprintf("  💵 %s: %s\n", service.I18n().T(ctx, "AdminDeposits"), flow.Deposits.StringFixed(2))
			}
			if !flow.Withdrawals.IsZero() {
				msg += fmt.Sprintf("  💸 %s: %s\n", service.I18n().T(ctx, "AdminWithdrawals"), flow.Withdrawals.StringFixed(2))
			}
			if !flow.Bets.IsZero() {
				msg += fmt.Sprintf("  🎲 %s: %s\n", service.I18n().T(ctx, "AdminBets"), flow.Bets.StringFixed(2))
			}
			if !flow.Wins.IsZero() {
				msg += fmt.Sprintf("  🏆 %s: %s\n", service.I18n().T(ctx, "AdminWins"), flow.Wins.StringFixed(2))
			}
		}
	}

	return msg
}

// BuildPersonalStatsKeyboard builds keyboard for personal stats search
func BuildPersonalStatsKeyboard(ctx context.Context) tgbotapi.InlineKeyboardMarkup {
	backBtn := tgbotapi.NewInlineKeyboardButtonData(
		"🔙 "+service.I18n().T(ctx, "AdminButtonBackToAdmin"),
		"admin_center",
	)

	return tgbotapi.NewInlineKeyboardMarkup(
		tgbotapi.NewInlineKeyboardRow(backBtn),
	)
}

// BuildUserStatsKeyboard builds keyboard for user statistics display
func BuildUserStatsKeyboard(ctx context.Context, userID uint64, status int) tgbotapi.InlineKeyboardMarkup {
	// Flow date range buttons
	// todayBtn := tgbotapi.NewInlineKeyboardButtonData(
	// 	"📅 "+service.I18n().T(ctx, "AdminButtonFlowToday"),
	// 	fmt.Sprintf("admin_personal_flow:%d:today", userID),
	// )
	// yesterdayBtn := tgbotapi.NewInlineKeyboardButtonData(
	// 	"📅 "+service.I18n().T(ctx, "AdminButtonFlowYesterday"),
	// 	fmt.Sprintf("admin_personal_flow:%d:yesterday", userID),
	// )
	// weekBtn := tgbotapi.NewInlineKeyboardButtonData(
	// 	"📅 "+service.I18n().T(ctx, "AdminButtonFlowWeek"),
	// 	fmt.Sprintf("admin_personal_flow:%d:week", userID),
	// )
	// monthBtn := tgbotapi.NewInlineKeyboardButtonData(
	// 	"📅 "+service.I18n().T(ctx, "AdminButtonFlowMonth"),
	// 	fmt.Sprintf("admin_personal_flow:%d:month", userID),
	// )

	// Betting records button
	bettingBtn := tgbotapi.NewInlineKeyboardButtonData(
		"🎲 "+service.I18n().T(ctx, "AdminButtonBettingRecords"),
		fmt.Sprintf("admin_betting_records:%d", userID),
	)

	// Account changes button
	accountBtn := tgbotapi.NewInlineKeyboardButtonData(
		"💰 "+service.I18n().T(ctx, "AdminButtonAccountChanges"),
		fmt.Sprintf("admin_account_changes:%d", userID),
	)

	// Betting volume query button
	bettingVolumeBtn := tgbotapi.NewInlineKeyboardButtonData(
		"📊 查询流水",
		fmt.Sprintf("admin_betting_volume_query:%d", userID),
	)

	// Ban/Unban button
	var banBtn tgbotapi.InlineKeyboardButton
	if status == 1 { // Active
		banBtn = tgbotapi.NewInlineKeyboardButtonData(
			"🚫 "+service.I18n().T(ctx, "AdminButtonBanUser"),
			fmt.Sprintf("admin_personal_ban:%d:ban", userID),
		)
	} else { // Banned
		banBtn = tgbotapi.NewInlineKeyboardButtonData(
			"✅ "+service.I18n().T(ctx, "AdminButtonUnbanUser"),
			fmt.Sprintf("admin_personal_ban:%d:unban", userID),
		)
	}

	// Total data button (similar to platform stats V2)
	// totalDataBtn := tgbotapi.NewInlineKeyboardButtonData(
	// 	"📊 总数据",
	// 	fmt.Sprintf("admin_personal_stats_v2:%d", userID),
	// )

	// Back button
	backBtn := tgbotapi.NewInlineKeyboardButtonData(
		"🔙 "+service.I18n().T(ctx, "AdminButtonBackToSearch"),
		"admin_personal_stats",
	)

	return tgbotapi.NewInlineKeyboardMarkup(
		// Total data button (prominent position)
		// tgbotapi.NewInlineKeyboardRow(totalDataBtn),
		// Flow date range selection
		// tgbotapi.NewInlineKeyboardRow(todayBtn, yesterdayBtn),
		// tgbotapi.NewInlineKeyboardRow(weekBtn, monthBtn),
		// Records navigation
		tgbotapi.NewInlineKeyboardRow(bettingBtn, accountBtn),
		// Betting volume query
		tgbotapi.NewInlineKeyboardRow(bettingVolumeBtn),
		// User management
		tgbotapi.NewInlineKeyboardRow(banBtn),
		// Navigation
		tgbotapi.NewInlineKeyboardRow(backBtn),
	)
}

// BuildUserStatsV2Keyboard builds keyboard for user statistics V2 display (similar to platform stats)
func BuildUserStatsV2Keyboard(ctx context.Context, userID uint64, status int) tgbotapi.InlineKeyboardMarkup {
	// Refresh button (similar to platform stats)
	refreshBtn := tgbotapi.NewInlineKeyboardButtonData(
		"🔄 "+service.I18n().T(ctx, "AdminButtonRefresh"),
		fmt.Sprintf("admin_personal_stats_v2:%d", userID),
	)

	// Back to detailed view button
	backToDetailBtn := tgbotapi.NewInlineKeyboardButtonData(
		"📊 详细统计",
		fmt.Sprintf("admin_personal_detail:%d", userID),
	)

	// Back to search button
	backBtn := tgbotapi.NewInlineKeyboardButtonData(
		"🔙 "+service.I18n().T(ctx, "AdminButtonBackToSearch"),
		"admin_personal_stats",
	)

	return tgbotapi.NewInlineKeyboardMarkup(
		// Refresh button
		tgbotapi.NewInlineKeyboardRow(refreshBtn),
		// Back to detailed view
		tgbotapi.NewInlineKeyboardRow(backToDetailBtn),
		// Navigation
		tgbotapi.NewInlineKeyboardRow(backBtn),
	)
}

// BuildFlowStatsKeyboard builds keyboard for flow statistics display
func BuildFlowStatsKeyboard(ctx context.Context, userID uint64) tgbotapi.InlineKeyboardMarkup {
	// Other date range buttons
	todayBtn := tgbotapi.NewInlineKeyboardButtonData(
		"📅 "+service.I18n().T(ctx, "AdminButtonFlowToday"),
		fmt.Sprintf("admin_personal_flow:%d:today", userID),
	)
	yesterdayBtn := tgbotapi.NewInlineKeyboardButtonData(
		"📅 "+service.I18n().T(ctx, "AdminButtonFlowYesterday"),
		fmt.Sprintf("admin_personal_flow:%d:yesterday", userID),
	)
	weekBtn := tgbotapi.NewInlineKeyboardButtonData(
		"📅 "+service.I18n().T(ctx, "AdminButtonFlowWeek"),
		fmt.Sprintf("admin_personal_flow:%d:week", userID),
	)
	monthBtn := tgbotapi.NewInlineKeyboardButtonData(
		"📅 "+service.I18n().T(ctx, "AdminButtonFlowMonth"),
		fmt.Sprintf("admin_personal_flow:%d:month", userID),
	)

	// Back to user stats
	backBtn := tgbotapi.NewInlineKeyboardButtonData(
		"🔙 "+service.I18n().T(ctx, "AdminButtonBackToUserStats"),
		fmt.Sprintf("admin_personal_detail:%d", userID),
	)

	return tgbotapi.NewInlineKeyboardMarkup(
		// Date range selection
		tgbotapi.NewInlineKeyboardRow(todayBtn, yesterdayBtn),
		tgbotapi.NewInlineKeyboardRow(weekBtn, monthBtn),
		// Navigation
		tgbotapi.NewInlineKeyboardRow(backBtn),
	)
}

// BuildBettingVolumeQueryMessage builds the date range input instruction message
func BuildBettingVolumeQueryMessage(ctx context.Context) string {
	msg := "📊 <b>查询用户流水</b>\n\n"
	msg += "请回复要查询的日期范围，使用空格分隔，例如：2025-01-01 2025-05-30\n\n"
	msg += "📝 格式说明：\n"
	msg += "• 日期格式：YYYY-MM-DD\n"
	msg += "• 用空格分隔开始和结束日期\n"
	msg += "• 开始日期需小于等于结束日期"
	return msg
}

// BuildBettingVolumeQueryKeyboard builds keyboard for betting volume query
func BuildBettingVolumeQueryKeyboard(ctx context.Context, userID uint64) tgbotapi.InlineKeyboardMarkup {
	keyboard := tgbotapi.NewInlineKeyboardMarkup(
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData(
				"📊 返回个人详细统计",
				fmt.Sprintf("admin_personal_detail:%d", userID),
			),
		),
	)

	return keyboard
}
