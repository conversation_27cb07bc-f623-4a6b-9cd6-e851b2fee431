package service

import (
	"context"
	"telegram-bot-api/internal/model/entity"

	"github.com/shopspring/decimal"
)

// ClaimResult 定义领取红包成功时的返回结果
type ClaimResult struct {
	Amount decimal.Decimal `json:"amount"` // 领取的金额
	Symbol string          `json:"symbol"` // 代币符号
}

// IRedPacket defines the interface for red packet related operations.
type IRedPacket interface {
	// CreateRedPacket 已移除，请使用 CreateRedPacketV2
	CreateRedPacketV2(ctx context.Context, input CreateRedPacketInput) (*entity.RedPackets, error)
	GetRedPacketByUUID(ctx context.Context, uuid string) (*entity.RedPackets, error)
	ClaimRedPacket(ctx context.Context, userID int64, redPacketUUID string, receiverUsername string) (*ClaimResult, error)
	ClaimRedPacketWithVerification(ctx context.Context, userID int64, redPacketUUID string, receiverUsername string, question string, correctAnswer int, userAnswer int) (*ClaimResult, error)
	StoreRedPacketInfo(ctx context.Context, redPacketUUID string, inlineMessageID string) error
	GetSentRedPacketsPage(ctx context.Context, userId int64, statusFilter []string, page, pageSize int) ([]*entity.RedPackets, int, error)
	GetRedPacketClaimsPage(ctx context.Context, redPacketId int64, page, pageSize int) ([]*entity.RedPacketClaims, int, error)
	CancelRedPacket(ctx context.Context, redPacketID int64, userID int64) error

	// 红包过期处理
	HandleExpiredRedPackets(ctx context.Context) ([]*entity.RedPackets, error)
	ExpireRedPacket(ctx context.Context, uuid string) (*entity.RedPackets, error)

	// CreateRedPacketClaims creates red packet claim records for pre-generated sub red packets
	CreateRedPacketClaims(ctx context.Context, tx interface{}, packet *entity.RedPackets, input CreateRedPacketInput, tenantId int, userID int64) error
}

// CreateRedPacketInput defines the input parameters for creating a red packet.
type CreateRedPacketInput struct {
	UserId            int64           `json:"userId" v:"required"`                        // 创建者用户 ID (Telegram ID)
	CreatorUserId     int64           `json:"creatorUserId" v:"required"`                 // 创建者用户 ID (Telegram ID)
	CreatorUsername   string          `json:"creatorUsername"   v:"required"`             // 创建者用户 ID (外键, 指向 users.user_id)
	ChatId            int64           `json:"chatId"        v:"required"`                 // 目标聊天 ID
	TokenId           uint            `json:"tokenId"       v:"required"`                 // 代币数据库 ID
	TokenSymbol       string          `json:"tokenSymbol"   v:"required"`                 // 代币符号 (e.g., USDT)
	Type              string          `json:"type"          v:"required|in:random,fixed"` // 红包类型: random, fixed
	Quantity          int             `json:"quantity"      v:"required|min:1|max:100"`   // 红包数量 (最大100个)
	TotalAmount       decimal.Decimal `json:"totalAmount"   v:"required"`                 // 红包总金额
	Blessing          string          `json:"blessing"`                                   // 祝福语 (optional)
	CoverId           int64           `json:"coverId"`
	RedPacketImagesId int64           `json:"redPacketImagesId" v:"required"` // 封面数据库 ID (optional, 0 if default)
	CoverFileID       string          `json:"cover_file_id" v:"required"`     // Telegram File ID
	ThumbUrl          string          `json:"thumbUrl"`                       // 封面图片URL
	MessageId         int             `json:"messageId"`                      // 创建红包的消息 ID
	// Group red packet specific fields
	RedPacketType    string `json:"redPacketType"`    // 红包类型: private, group
	CommandType      string `json:"commandType"`      // 命令类型: hb, xyhb (for group red packets)
	IsGroupRedPacket bool   `json:"isGroupRedPacket"` // 是否为群组红包
	// 领取条件字段 - 注意：这些字段由调用方设置，不会自动复制历史红包的值
	BettingVolume     decimal.Decimal `json:"bettingVolume"`     // 流水金额要求（由用户输入或命令解析得出，不复制历史红包）
	BettingVolumeDays int             `json:"bettingVolumeDays"` // 流水天数要求（当前实现为0，不复制历史红包）
	SpecifyBetting    int             `json:"specifyBetting"`    // 是否需要流水（1=需要，0=不需要，根据BettingVolume是否有值决定）
}

type sRedPacket struct{}

// NewRedPacket creates and returns a new instance of the service.
func NewRedPacket() *sRedPacket {
	return &sRedPacket{}
}

func init() {
	// RegisterRedPacket(NewRedPacket()) // Will be registered by logic layer
}

var localRedPacket IRedPacket

// RegisterRedPacket registers the service implementation.
func RegisterRedPacket(i IRedPacket) {
	localRedPacket = i
}

// RedPacket returns the registered service instance.
func RedPacket() IRedPacket {
	if localRedPacket == nil {
		panic("implement not found for interface IRedPacket, forgot register?")
	}
	return localRedPacket
}
