package red_packet

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"time"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/google/uuid"
	"github.com/shopspring/decimal"

	config "telegram-bot-api/internal/config"
	"telegram-bot-api/internal/constants"
	localConsts "telegram-bot-api/internal/consts"
	"telegram-bot-api/internal/dao"
	"telegram-bot-api/internal/model/entity"
	"telegram-bot-api/internal/service"
	"telegram-bot-api/internal/utils"

	"github.com/yalks/wallet"
	walletConstants "github.com/yalks/wallet/constants"

	"github.com/a19ba14d/tg-bot-common/codes"
	"github.com/a19ba14d/tg-bot-common/consts"
)

// CreateRedPacketV2 使用统一资金操作服务的红包创建逻辑
// 这个版本展示了如何简化红包创建流程，确保资金操作和业务逻辑的一致性
func (s *sRedPacket) CreateRedPacketV2(ctx context.Context, input service.CreateRedPacketInput) (*entity.RedPackets, error) {
	g.Log().Infof(ctx, "CreateRedPacketV2: Starting operation for user %d", input.CreatorUserId)

	// 1. 输入验证
	if err := s.validateRedPacketInput(ctx, input); err != nil {
		return nil, err
	}

	// 1.5. 群组红包特殊验证
	if input.IsGroupRedPacket {
		if err := s.validateGroupRedPacketInput(ctx, input); err != nil {
			return nil, err
		}
	}

	// 1.6. 风险控制检查（仅对群组红包）
	if input.IsGroupRedPacket {
		riskControl := service.GetGroupRedPacketRiskControl()
		riskResult, err := riskControl.CheckCreationRisk(ctx, input.CreatorUserId, input.TotalAmount, input.Quantity, input.TokenSymbol)
		if err != nil {
			g.Log().Errorf(ctx, "CreateRedPacketV2: Risk control check failed: %v", err)
			return nil, gerror.NewCode(codes.CodeDatabaseError, service.I18n().T(ctx, "{#SystemError}"))
		}

		if !riskResult.Allowed {
			g.Log().Warningf(ctx, "CreateRedPacketV2: Creation blocked by risk control for user %d: %s", input.CreatorUserId, riskResult.Reason)
			// Record failed attempt
			riskControl.RecordCreationAttempt(ctx, input.CreatorUserId, input.TotalAmount, input.TokenSymbol, false)
			return nil, gerror.NewCode(codes.CodeError, riskResult.Reason)
		}
	}

	// 2. 获取用户信息
	user, err := service.User().GetUserByTelegramId(ctx, input.CreatorUserId)
	if err != nil || user == nil {
		g.Log().Errorf(ctx, "CreateRedPacketV2: Failed to find user %d: %v", input.CreatorUserId, err)
		return nil, gerror.NewCode(codes.CodeUserNotFound, "Failed to get user information")
	}

	var createdPacket *entity.RedPackets
	var financialResult *wallet.FundOperationResult

	// 3. 在单个事务中处理所有操作
	dbErr := g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 3a. 执行资金扣款 - 使用新的钱包模块
		// 生成原始UUID并使用MD5缩短，以避免回调数据超过Telegram的64字节限制
		// 原始UUID: 36字符 (如: a9590b3f-f935-4a8d-bd22-ee57a895b2a5)
		// MD5缩短后: 16字符 (如: a9590b3ff9354a8d)
		originalUUID := uuid.NewString()
		hash := md5.Sum([]byte(originalUUID))
		redPacketUUID := hex.EncodeToString(hash[:])[:16] // 使用MD5的前16个字符

		// 确保UUID唯一性（虽然碰撞概率极低）
		for {
			exists, err := dao.RedPackets.Ctx(ctx).TX(tx).Where("uuid", redPacketUUID).Count()
			if err != nil {
				return gerror.Wrap(err, "failed to check UUID uniqueness")
			}
			if exists == 0 {
				break
			}
			// 如果碰撞，生成新的UUID
			originalUUID = uuid.NewString()
			hash = md5.Sum([]byte(originalUUID))
			redPacketUUID = hex.EncodeToString(hash[:])[:16]
		}

		// 使用新的资金操作系统
		descriptor := utils.NewFundOperationDescriptor("zh")
		req := &walletConstants.FundOperationRequest{
			UserID:      uint64(user.Id),
			TokenSymbol: input.TokenSymbol,
			Amount:      input.TotalAmount,
			BusinessID:  descriptor.GenerateBusinessID(constants.FundOpRedPacketCreate, redPacketUUID, gtime.Now().Unix()),
			FundType:    walletConstants.FundTypeRedPacketCreate,
			Description: descriptor.FormatBasicDescription(constants.FundOpRedPacketCreate, input.TotalAmount.String(), input.TokenSymbol),
			Metadata: map[string]string{
				"type":      "red_packet",
				"chat_id":   gconv.String(input.ChatId),
				"quantity":  gconv.String(input.Quantity),
				"rp_type":   input.Type,
				"blessing":  input.Blessing,
				"operation": "create",
			},
			RequestSource: "telegram",
		}

		var err error
		financialResult, err = wallet.Manager().ProcessFundOperationInTx(ctx, tx, req)
		if err != nil {
			g.Log().Errorf(ctx, "CreateRedPacketV2: 红包扣款失败 for user %d: %v", input.CreatorUserId, err)
			return err // 新钱包模块已经处理了错误分类和回滚
		}

		// 3b. 创建红包主记录
		expireMinutes := g.Cfg().MustGet(ctx, "timeouts.redPacketExpirationMinutes", 1440).Int()
		expireDuration := time.Duration(expireMinutes) * time.Minute
		now := gtime.Now()

		// 处理祝福语：如果祝福语为空，保持为空
		blessing := input.Blessing
		// No default blessing - keep it empty if not provided

		// Determine red packet type and draw status
		redPacketType := localConsts.RedPacketTypePrivate
		var drawStatus string // Empty string represents NULL in database
		var chatId int64

		if input.IsGroupRedPacket {
			redPacketType = localConsts.RedPacketTypeGroup
			chatId = input.ChatId

			// Set draw status for lucky red packets (xyhb command)
			if input.CommandType == localConsts.GroupRedPacketCommandXYHB {
				drawStatus = localConsts.DrawStatusPending
			}
			// For regular group red packets (hb command), drawStatus remains empty
		}
		// For private red packets, drawStatus remains empty

		mainPacket := entity.RedPackets{
			Uuid:              redPacketUUID,
			CreatorUserId:     input.CreatorUserId,
			CreatorUsername:   input.CreatorUsername,
			RedPacketImagesId: input.RedPacketImagesId,
			TokenId:           int(input.TokenId),
			Symbol:            input.TokenSymbol,
			Type:              input.Type,
			RedPacketType:     redPacketType, // 新增：红包类型 (private/group)
			DrawStatus:        drawStatus,    // 新增：抽奖状态 (仅幸运红包)
			DrawTime:          nil,           // 新增：开奖时间 (开奖时设置)
			ChatId:            chatId,        // 新增：群组 ChatID (仅群组红包)
			Quantity:          input.Quantity,
			TotalAmount:       input.TotalAmount,
			RemainingAmount:   input.TotalAmount,
			RemainingQuantity: input.Quantity,
			Memo:              blessing, // 使用处理后的祝福语
			Status:            string(consts.RedPacketStatusActive),
			CreatedAt:         now,
			ExpiresAt:         now.Add(expireDuration),
			CoverFileId:       input.CoverFileID,
			ThumbUrl:          input.ThumbUrl,
			SenderUserId:      uint64(user.Id),                                     // 设置发送方用户ID (使用内部数据库ID)
			TransactionId:     s.parseTransactionID(financialResult.TransactionID), // 关联交易流水
			MessageId:         gconv.String(input.MessageId),                       // 记录创建红包的消息ID
			IsPremium:         0,                                                   // 默认为非会员红包
			TenantId:          user.TenantId,
			// 领取条件设置 - 这些值直接从input传入，不会复制历史红包的设置
			BettingVolume:     input.BettingVolume,     // 流水金额要求（从创建输入中获取，不复制历史红包）
			BettingVolumeDays: input.BettingVolumeDays, // 流水天数要求（从创建输入中获取，不复制历史红包）
			SpecifyBetting:    input.SpecifyBetting,    // 是否需要流水（1=需要，0=不需要，从创建输入中获取）
		}

		result, err := dao.RedPackets.Ctx(ctx).TX(tx).Data(mainPacket).Insert()
		if err != nil {
			g.Log().Errorf(ctx, "CreateRedPacketV2: Failed to insert main red packet record: %v", err)
			return gerror.Wrap(err, "failed to create main red packet record")
		}

		packetId, _ := result.LastInsertId()
		mainPacket.RedPacketId = packetId
		createdPacket = &mainPacket

		g.Log().Infof(ctx, "CreateRedPacketV2: Created red packet ID %d with UUID %s and transaction ID %s",
			packetId, redPacketUUID, financialResult.TransactionID)

		// 3c. 创建红包分配记录
		if err := s.CreateRedPacketClaims(ctx, tx, createdPacket, input, user.TenantId, int64(user.Id)); err != nil {
			return gerror.Wrap(err, "failed to create red packet claims")
		}

		return nil
	})

	// 4. 处理事务结果
	if dbErr != nil {
		g.Log().Errorf(ctx, "CreateRedPacketV2: Transaction failed for user %d: %v", input.CreatorUserId, dbErr)
		return nil, dbErr
	}

	// 5. Record successful creation

	// 5.1. Record successful risk control attempt for group red packets
	if input.IsGroupRedPacket {
		riskControl := service.GetGroupRedPacketRiskControl()
		riskControl.RecordCreationAttempt(ctx, input.CreatorUserId, input.TotalAmount, input.TokenSymbol, true)
	}

	// 6. 返回成功结果
	g.Log().Infof(ctx, "CreateRedPacketV2: Successfully created red packet ID %d (TxID: %d) for user %d",
		createdPacket.RedPacketId, createdPacket.TransactionId, input.CreatorUserId)
	return createdPacket, nil
}

// validateRedPacketInput 验证红包输入参数
func (s *sRedPacket) validateRedPacketInput(ctx context.Context, input service.CreateRedPacketInput) error {
	// 1. 验证红包数量范围
	minNum, err := config.GetInt(ctx, "red_packet_setting.min_num", 1)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to get min_num from config: %v", err)
		minNum = 1 // 使用默认值
	}

	maxNum, err := config.GetInt(ctx, "red_packet_setting.max_num", consts.RedPacketMaxQuantity)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to get max_num from config: %v", err)
		maxNum = consts.RedPacketMaxQuantity // 使用默认值
	}

	if input.Quantity < minNum {
		return gerror.NewCodef(codes.CodeValidationFailed, "Red packet quantity must be at least %d", minNum)
	}
	if input.Quantity > maxNum {
		return gerror.NewCodef(codes.CodeValidationFailed, "Red packet quantity cannot exceed %d", maxNum)
	}

	// 2. 验证总金额
	if input.TotalAmount.LessThanOrEqual(decimal.Zero) {
		return gerror.NewCode(codes.CodeValidationFailed, "Total red packet amount must be greater than 0")
	}

	// 3. 获取代币信息以获取正确的小数位数
	tokenInfo, err := service.Token().GetTokenBySymbol(ctx, input.TokenSymbol)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to get token info for %s: %v", input.TokenSymbol, err)
		return gerror.Wrapf(err, "failed to get token info for %s", input.TokenSymbol)
	}
	if tokenInfo == nil {
		return gerror.Newf("token %s not found", input.TokenSymbol)
	}

	// 4. 验证单个红包最小金额
	minAmountMap, err := config.GetMap(ctx, "red_packet_setting.min_single_amount")
	if err != nil {
		g.Log().Errorf(ctx, "Failed to get min_single_amount from config: %v", err)
		return gerror.Wrapf(err, "failed to get red packet min amount config")
	}

	if minAmountMap == nil {
		g.Log().Errorf(ctx, "min_single_amount config is nil")
		return gerror.New("red packet min amount config is not available")
	}

	var minClaimAmount decimal.Decimal
	if minAmountValue := minAmountMap[input.TokenSymbol]; minAmountValue != nil {
		minClaimAmount, err = decimal.NewFromString(minAmountValue.(string))
		if err != nil {
			g.Log().Errorf(ctx, "Failed to parse min_single_amount for %s: %v", input.TokenSymbol, err)
			return gerror.Wrapf(err, "failed to parse min amount for token %s", input.TokenSymbol)
		}
	} else {
		// 如果没有配置特定代币的最小金额，使用默认值
		minClaimAmount = decimal.NewFromFloat(consts.RedPacketMinClaimAmount)
		g.Log().Warningf(ctx, "No min_single_amount config found for token %s, using default: %s", input.TokenSymbol, minClaimAmount.String())
	}

	// 使用代币的实际小数位数进行计算
	tokenDecimals := int32(tokenInfo.Decimals)

	if input.Type == string(consts.RedPacketTypeFixed) {
		singleAmount := input.TotalAmount.DivRound(decimal.NewFromInt(int64(input.Quantity)), tokenDecimals)
		if singleAmount.LessThan(minClaimAmount) {
			return gerror.NewCodef(codes.CodeValidationFailed, "Single fixed red packet amount cannot be less than %s", minClaimAmount.String())
		}
	} else { // Random
		avgAmount := input.TotalAmount.DivRound(decimal.NewFromInt(int64(input.Quantity)), tokenDecimals)
		if avgAmount.LessThan(minClaimAmount) {
			return gerror.NewCodef(codes.CodeValidationFailed, "Average random red packet amount cannot be less than %s", minClaimAmount.String())
		}
	}

	return nil
}

// validateGroupRedPacketInput validates group-specific red packet input parameters
func (s *sRedPacket) validateGroupRedPacketInput(ctx context.Context, input service.CreateRedPacketInput) error {
	// 1. Validate chat ID is negative (group chat)
	if input.ChatId >= 0 {
		return gerror.NewCode(codes.CodeValidationFailed, "Group red packet requires a negative chat ID (group chat)")
	}

	// 2. Validate command type for group red packets
	if input.CommandType != localConsts.GroupRedPacketCommandHB &&
		input.CommandType != localConsts.GroupRedPacketCommandXYHB {
		return gerror.NewCodef(codes.CodeValidationFailed, "Invalid command type for group red packet: %s", input.CommandType)
	}

	// 3. Validate group red packet quantity limits
	if input.Quantity > localConsts.MaxGroupRedPacketQuantity {
		return gerror.NewCodef(codes.CodeValidationFailed, "Group red packet quantity cannot exceed %d", localConsts.MaxGroupRedPacketQuantity)
	}

	// 4. Validate minimum amount per red packet for group red packets
	minAmount := decimal.NewFromFloat(localConsts.MinGroupRedPacketAmount)
	avgAmount := input.TotalAmount.DivRound(decimal.NewFromInt(int64(input.Quantity)), 8) // Use 8 decimal places for calculation
	if avgAmount.LessThan(minAmount) {
		return gerror.NewCodef(codes.CodeValidationFailed, "Average group red packet amount cannot be less than %s %s",
			minAmount.String(), input.TokenSymbol)
	}

	// 5. Check if user is a member of the group (optional validation)
	// This could be implemented if we want to ensure the user is actually in the group
	// For now, we'll skip this validation as it might be too restrictive

	g.Log().Infof(ctx, "Group red packet validation passed for user %d in chat %d", input.CreatorUserId, input.ChatId)
	return nil
}

// CreateRedPacketClaims creates red packet claim records for pre-generated sub red packets
func (s *sRedPacket) CreateRedPacketClaims(ctx context.Context, tx interface{}, packet *entity.RedPackets, input service.CreateRedPacketInput, tenantId int, userID int64) error {
	// Type assert to gdb.TX
	dbTx, ok := tx.(gdb.TX)
	if !ok {
		return gerror.New("invalid transaction type")
	}
	// 获取代币信息以获取正确的小数位数
	tokenInfo, err := service.Token().GetTokenBySymbol(ctx, input.TokenSymbol)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to get token info for %s: %v", input.TokenSymbol, err)
		return gerror.Wrapf(err, "failed to get token info for %s", input.TokenSymbol)
	}
	if tokenInfo == nil {
		return gerror.Newf("token %s not found", input.TokenSymbol)
	}

	// 获取配置的最小金额
	minAmountMap, err := config.GetMap(ctx, "red_packet_setting.min_single_amount")
	if err != nil {
		g.Log().Errorf(ctx, "Failed to get min_single_amount from config: %v", err)
		return gerror.Wrapf(err, "failed to get red packet min amount config")
	}

	var minClaimAmount decimal.Decimal
	if minAmountMap != nil && minAmountMap[input.TokenSymbol] != nil {
		var parseErr error
		minClaimAmount, parseErr = decimal.NewFromString(minAmountMap[input.TokenSymbol].(string))
		if parseErr != nil {
			g.Log().Errorf(ctx, "Failed to parse min_single_amount for %s: %v", input.TokenSymbol, parseErr)
			minClaimAmount = decimal.NewFromFloat(consts.RedPacketMinClaimAmount) // 使用默认值
		}
	} else {
		minClaimAmount = decimal.NewFromFloat(consts.RedPacketMinClaimAmount) // 使用默认值
	}

	// 使用代币的实际小数位数
	tokenDecimals := int32(tokenInfo.Decimals)

	// 计算每个红包的金额
	var individualAmounts []decimal.Decimal

	if input.Type == string(consts.RedPacketTypeFixed) {
		individualAmounts = s.allocateFixedAmounts(input.TotalAmount, input.Quantity, tokenDecimals)
	} else { // Random
		individualAmounts, err = AllocateRandomAmounts(input.TotalAmount, input.Quantity, minClaimAmount, tokenDecimals)
		if err != nil {
			g.Log().Errorf(ctx, "CreateRedPacketV2: Failed to allocate random amounts: %v", err)
			return gerror.Wrap(err, "failed to allocate red packet amounts")
		}
	}

	// 创建分配记录
	claims := make([]*entity.RedPacketClaims, 0, input.Quantity)
	for _, amount := range individualAmounts {
		if amount.LessThanOrEqual(decimal.Zero) {
			g.Log().Errorf(ctx, "CreateRedPacketV2: Calculated zero or negative claim amount for packet %d", packet.RedPacketId)
			return gerror.New("failed to calculate red packet claim amount")
		}
		claims = append(claims, &entity.RedPacketClaims{
			RedPacketId:    packet.RedPacketId,
			Amount:         amount,
			Symbol:         input.TokenSymbol,
			Status:         string(consts.RedPacketClaimStatusPending),
			SenderUserId:   uint64(userID), // 使用 users 表的 ID 而不是 Telegram ID
			SenderUsername: input.CreatorUsername,
			ClaimedAt:      nil,
			TenantId:       tenantId,
		})
	}

	_, err = dao.RedPacketClaims.Ctx(ctx).TX(dbTx).Data(claims).Insert()
	if err != nil {
		g.Log().Errorf(ctx, "CreateRedPacketV2: Failed to bulk insert red packet claims for packet %d: %v", packet.RedPacketId, err)
		return gerror.Wrap(err, "failed to create red packet claim records")
	}

	g.Log().Infof(ctx, "CreateRedPacketV2: Inserted %d claims for packet ID %d", len(claims), packet.RedPacketId)
	return nil
}

// allocateFixedAmounts 分配固定金额红包
func (s *sRedPacket) allocateFixedAmounts(totalAmount decimal.Decimal, quantity int, decimals int32) []decimal.Decimal {
	singleAmount := totalAmount.DivRound(decimal.NewFromInt(int64(quantity)), decimals)
	individualAmounts := make([]decimal.Decimal, quantity)

	for i := 0; i < quantity; i++ {
		individualAmounts[i] = singleAmount
	}

	// 处理除不尽的情况，将余额加到最后一个红包
	currentSum := singleAmount.Mul(decimal.NewFromInt(int64(quantity)))
	diff := totalAmount.Sub(currentSum)
	if !diff.IsZero() {
		individualAmounts[quantity-1] = individualAmounts[quantity-1].Add(diff)
	}

	return individualAmounts
}

// 对比新旧版本的优势：
//
// 1. 代码简洁性：
//    - 旧版本：245行，包含复杂的钱包API调用和手动事务管理
//    - 新版本：约200行，逻辑更清晰，专注于业务逻辑
//
// 2. 错误处理：
//    - 旧版本：需要手动检查钱包API错误类型，处理余额不足等情况
//    - 新版本：统一资金操作服务自动处理错误分类和验证
//
// 3. 事务一致性：
//    - 旧版本：钱包操作在事务内部，但错误处理复杂
//    - 新版本：所有操作在统一事务中，自动回滚
//
// 4. 幂等性：
//    - 旧版本：没有幂等性保护
//    - 新版本：通过BusinessID自动处理幂等性
//
// 5. 审计追踪：
//    - 旧版本：手动记录交易流水，需要额外的更新操作
//    - 新版本：统一服务自动记录，直接关联到红包记录
//
// 6. 可维护性：
//    - 旧版本：资金操作逻辑分散，难以统一管理
//    - 新版本：资金操作统一管理，业务逻辑更专注

// parseTransactionID 将字符串格式的交易ID转换为uint64
func (s *sRedPacket) parseTransactionID(transactionID string) uint64 {
	if txID, err := decimal.NewFromString(transactionID); err == nil {
		return uint64(txID.IntPart())
	}
	// 如果解析失败，返回0（这种情况不应该发生，但作为容错处理）
	g.Log().Warningf(context.Background(), "Failed to parse transaction ID: %s", transactionID)
	return 0
}
