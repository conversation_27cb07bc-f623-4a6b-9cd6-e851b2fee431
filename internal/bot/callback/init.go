package callback

import (
	"context"

	tgbotapi "github.com/a19ba14d/telegram-bot-api/v5"
	"github.com/gogf/gf/v2/frame/g"

	localConsts "telegram-bot-api/internal/consts"
	"telegram-bot-api/internal/model/callback"
	"telegram-bot-api/internal/registry"
)

func init() {
	// Register callback handlers for group red packets
	registerGroupRedPacketCallbacks()
}

// registerGroupRedPacketCallbacks registers all group red packet related callback handlers
func registerGroupRedPacketCallbacks() {
	ctx := context.Background()
	g.Log().Infof(ctx, "Registering group red packet callback handlers...")

	// Register group red packet claim handler
	registry.RegisterPrefix(localConsts.CallbackPrefixGroupRedPacketClaim+":", groupRedPacketCallbackWrapper)

	// Register lucky red packet participate handler
	registry.RegisterPrefix(localConsts.CallbackPrefixLuckyRedPacketParticipate+":", groupRedPacketCallbackWrapper)

	// Register lucky red packet draw handler
	registry.RegisterPrefix(localConsts.CallbackPrefixLuckyRedPacketDraw+":", groupRedPacketCallbackWrapper)

	// Register lucky results handler
	registry.RegisterPrefix("lucky_results:", groupRedPacketCallbackWrapper)

	// Register red packet pagination handler
	registry.RegisterPrefix("rp_page:", groupRedPacketCallbackWrapper)

	// Register red packet close handlers for details/claim list only
	registry.RegisterPrefix("rp:close_details", groupRedPacketCallbackWrapper)
	registry.RegisterPrefix("rp:close_claim_list", groupRedPacketCallbackWrapper)

	g.Log().Infof(ctx, "Group red packet callback handlers registered successfully")
}

// groupRedPacketCallbackWrapper wraps HandleGroupRedPacketCallback to match the registry signature
func groupRedPacketCallbackWrapper(ctx context.Context, query *tgbotapi.CallbackQuery) (callback.CallbackResponse, error) {
	// Call the actual handler
	err := HandleGroupRedPacketCallback(ctx, query)
	if err != nil {
		// Return error to be handled by the callback processor
		return nil, err
	}

	// Return nil response as the handler manages its own responses
	return nil, nil
}
