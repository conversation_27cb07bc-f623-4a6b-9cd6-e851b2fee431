package redpacket

import (
	"context"
	"errors"
	"fmt"
	"telegram-bot-api/internal/bot/shared"
	localConsts "telegram-bot-api/internal/consts"
	"telegram-bot-api/internal/dao"

	"github.com/a19ba14d/tg-bot-common/consts"

	// "telegram-bot-api/internal/model" // Removed unused import
	"telegram-bot-api/internal/model/callback"
	"telegram-bot-api/internal/model/entity"
	"telegram-bot-api/internal/service"

	tgbotapi "github.com/a19ba14d/telegram-bot-api/v5"
	"github.com/gogf/gf/v2/errors/gcode" // Import gcode
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv" // Import gconv
	"github.com/shopspring/decimal"    // Import decimal
)

// handleRedPacketPasswordConfirm handles the confirm action after entering the red packet payment password.
func handleRedPacketPasswordConfirm(ctx context.Context, cq *tgbotapi.CallbackQuery, _ []string) (callback.CallbackResponse, error) {
	userId := cq.From.ID
	username := cq.From.UserName
	chatID := cq.Message.Chat.ID
	messageID := cq.Message.MessageID
	i18n := service.I18n().Instance()

	// 1. Get user state and validate
	userState, err := service.UserState().GetUserStateByTelegramId(ctx, userId)
	if err != nil {
		g.Log().Errorf(ctx, "handleRedPacketPasswordConfirm: Error getting user state for user %d: %v", userId, err)
		return callback.NewAlertResponse(i18n.T(ctx, "{#SystemError}")), err
	}
	if userState == nil || userState.State != consts.UserStateWaitingRedPacketPaymentPassword {
		g.Log().Warningf(ctx, "handleRedPacketPasswordConfirm: Invalid state for user %d. Expected %s, got %v", userId, consts.UserStateWaitingRedPacketPaymentPassword, userState)
		return callback.NewAlertResponse(i18n.T(ctx, "{#InvalidOperationOrExpired}")), nil
	}
	if userState.Context == nil {
		g.Log().Errorf(ctx, "handleRedPacketPasswordConfirm: User state context is nil for user %d", userId)
		_ = service.UserState().ClearUserStateByTelegramId(ctx, userId) // Attempt cleanup
		return callback.NewAlertResponse(i18n.T(ctx, "{#SystemError}")), gerror.New("Missing user state context")
	}

	// 2. Extract password from context
	currentPassword := userState.Context[consts.RpContextKeyCurrentPassword]

	// 3. Validate password length
	if len(currentPassword) != 6 {
		g.Log().Warningf(ctx, "handleRedPacketPasswordConfirm: User %d submitted password with incorrect length (%d)", userId, len(currentPassword))
		// Keep state, edit message to show error? Or just alert? Alert is simpler.
		alertText := i18n.T(ctx, "{#PaymentPasswordExactLength}") // Need i18n key
		return callback.NewAlertResponse(alertText), nil
	}

	// 4. Call verification service
	verificationErr := shared.VerifyPaymentPassword(ctx, userId, currentPassword, consts.BusinessTypeRedPacket)

	// 5. Handle verification result
	var responseText string
	var finalKeyboard *tgbotapi.InlineKeyboardMarkup // nil to remove keyboard on success/lock/other errors
	clearState := true                               // Clear state by default on success or non-retryable error

	if verificationErr == nil {
		// --- Success: Password Verified ---
		g.Log().Infof(ctx, "handleRedPacketPasswordConfirm: Password verified for user %d. Proceeding to create red packet.", userId)

		// --- Prepare parameters for CreateRedPacket service ---
		creatorUserID := userId // Use Telegram User ID from callback query
		chatID := gconv.Int64(userState.Context[consts.RpContextKeyChatID])
		tokenSymbol := userState.Context[consts.RpContextKeyTokenSymbol]
		rpType := userState.Context[consts.RpContextKeyType]
		quantity := gconv.Int(userState.Context[consts.RpContextKeyQuantity])
		totalAmountStr := userState.Context[consts.RpContextKeyTotalAmount]
		blessing := userState.Context[consts.RpContextKeyBlessing]
		coverFileID := userState.Context[consts.RpContextKeySelectedCoverFileId]                          // Telegram File ID
		g.Log().Debugf(ctx, "Selected cover file ID: %s (DB ID lookup not implemented yet)", coverFileID) // Log the unused variable

		// Convert total amount string to decimal
		totalAmountDecimal, convErr := decimal.NewFromString(totalAmountStr)
		if convErr != nil {
			g.Log().Errorf(ctx, "handleRedPacketPasswordConfirm: Invalid total amount format '%s' in context for user %d: %v", totalAmountStr, creatorUserID, convErr)
			responseText = i18n.T(ctx, "{#SystemError}") // Use generic system error
			// Clear state is already true
		} else {
			// Get Token entity by symbol to find its ID
			token, tokenErr := service.Token().GetTokenBySymbol(ctx, tokenSymbol)
			if tokenErr != nil || token == nil {
				g.Log().Errorf(ctx, "handleRedPacketPasswordConfirm: Failed to get token '%s' for user %d: %v", tokenSymbol, creatorUserID, tokenErr)
				responseText = i18n.T(ctx, "{#SystemError}") // Use generic system error
				// Clear state is already true
			} else {
				// Check if this is a no-cover red packet
				coverType := userState.Context[localConsts.RpContextKeyCoverType]
				var coverDbId int64 = 0
				var redPacketImagesId int64 = 0
				var createRedPacket = true

				if coverFileID == "" || coverType == localConsts.RpCoverTypeNone {
					// No cover red packet - skip image lookup
					g.Log().Debugf(ctx, "handleRedPacketPasswordConfirm: Creating no-cover red packet for user %d", creatorUserID)
				} else {
					// Existing logic for red packets with covers
					img, imgErr := service.RedPacketImage().GetImageByFileId(ctx, coverFileID)

					if imgErr != nil {
						g.Log().Errorf(ctx, "handleRedPacketPasswordConfirm: Error getting image by file ID %s for user %d: %v", coverFileID, creatorUserID, imgErr)
						responseText = i18n.T(ctx, "{#SystemError}") // Use generic system error
						createRedPacket = false
					} else if img == nil {
						g.Log().Errorf(ctx, "handleRedPacketPasswordConfirm: No image found for file ID %s for user %d", coverFileID, creatorUserID)
						responseText = i18n.T(ctx, "{#SystemError}") // Use generic system error
						createRedPacket = false
					} else {
						if img != nil {
							redPacketImagesId = img.RedPacketImagesId
						}
					}
				}

				if createRedPacket {
					userInfo, err := service.User().GetUserByTelegramId(ctx, creatorUserID)
					if err != nil {
						g.Log().Errorf(ctx, "handleRedPacketPasswordConfirm: Failed to get user info for creator username (pass-verified): %v", err)
						// Don't clear state
						return callback.NewAlertResponse(i18n.T(ctx, "{#SystemError}")), err
					}
					if userInfo == nil {
						g.Log().Errorf(ctx, "handleRedPacketPasswordConfirm: User info not found for user %d (pass-verified)", creatorUserID)
						// Don't clear state
						return callback.NewAlertResponse(i18n.T(ctx, "{#UserNotFound}")), gerror.New("User not found")
					}

					// Get telegram username from user_backup_accounts table
					var backupAccount *entity.UserBackupAccounts
					err = dao.UserBackupAccounts.Ctx(ctx).
						Where(dao.UserBackupAccounts.Columns().UserId, userInfo.Id).
						Where(dao.UserBackupAccounts.Columns().IsMaster, 1).
						Scan(&backupAccount)

					creatorUsername := username // Default fallback to Telegram username
					if err == nil && backupAccount != nil && backupAccount.TelegramUsername != "" {
						creatorUsername = backupAccount.TelegramUsername
					}

					// Build the input for the service
					// 注意：这里创建的是全新的红包，不会从任何历史红包复制配置
					// BettingVolume、BettingVolumeDays、SpecifyBetting等领取条件字段使用默认值（零值）
					input := service.CreateRedPacketInput{
						UserId:            int64(userInfo.Id),
						CreatorUserId:     creatorUserID,
						CreatorUsername:   creatorUsername,
						ChatId:            chatID,
						TokenId:           token.TokenId, // Use correct field name TokenId
						TokenSymbol:       tokenSymbol,   // Pass symbol along for convenience in service if needed
						Type:              rpType,
						Quantity:          quantity,
						TotalAmount:       totalAmountDecimal,
						Blessing:          blessing,
						CoverId:           coverDbId,
						RedPacketImagesId: redPacketImagesId, // Use the ID from the image or 0 if not found
						CoverFileID:       coverFileID,       // Pass Telegram File ID for now
						MessageId:         messageID,         // 记录创建红包的消息ID
						// 领取条件字段使用默认值，不复制历史红包：
						// BettingVolume: decimal.Zero (默认)
						// BettingVolumeDays: 0 (默认)
						// SpecifyBetting: 0 (默认，表示不需要流水)
					}
					g.Log().Infof(ctx, "Calling CreateRedPacket service for user %d with input: %+v", creatorUserID, input)

					// --- Call the CreateRedPacketV2 service ---
					createdPacket, createErr := service.RedPacket().CreateRedPacketV2(ctx, input)

					// --- Handle creation result ---
					if createErr != nil {
						g.Log().Errorf(ctx, "handleRedPacketPasswordConfirm: service.RedPacket().CreateRedPacket failed for user %d: %v", creatorUserID, createErr)
						// Try to get a more specific error message if it's a gerror with code
						if ge := gerror.Cause(createErr); ge != nil {
							if code := gerror.Code(ge); code != gcode.CodeNil {
								responseText = code.Message() // Use message from the error code
							} else {
								responseText = i18n.Tf(ctx, "{#RedPacketCreationFailed}", ge.Error()) // Use underlying error message
							}
						} else {
							responseText = i18n.Tf(ctx, "{#RedPacketCreationFailed}", createErr.Error()) // Fallback
						}
					} else {
						g.Log().Infof(ctx, "handleRedPacketPasswordConfirm: Red packet ID %d (UUID: %s) created successfully for user %d", createdPacket.RedPacketId, createdPacket.Uuid, creatorUserID) // Log UUID
						// Build success message with details using unified builder
						successTitle := i18n.T(ctx, "{#RedPacketCreatedSuccess}")
						builder := NewSuccessPageBuilder(ctx)
						rpInfo := BuildRedPacketInfoFromEntity(createdPacket, tokenSymbol)
						detailsText := builder.BuildConfirmationMessage(rpInfo)
						responseText = fmt.Sprintf("%s\n\n%s", successTitle, detailsText)

						// --- Create Share Button and Premium Toggle ---
						redPacketUUID := createdPacket.Uuid
						shareQuery := fmt.Sprintf("%s%s ", consts.InlineQueryPrefixShareRedPacket, redPacketUUID) // Use constant
						shareBtnText := i18n.T(ctx, "{#ShareRedPacketButton}")                                    // Use i18n key

						// Create keyboard rows
						rows := [][]tgbotapi.InlineKeyboardButton{
							// Share button row
							{
								tgbotapi.InlineKeyboardButton{Text: shareBtnText, SwitchInlineQuery: &shareQuery},
							},
						}

						// Add premium toggle button based on current status
						if createdPacket.IsPremium == 0 {
							// Add "Set as Premium" button
							setPremiumText := i18n.T(ctx, "{#SetAsPremiumRedPacket}")
							rows = append(rows, []tgbotapi.InlineKeyboardButton{
								tgbotapi.NewInlineKeyboardButtonData(setPremiumText, fmt.Sprintf("rp:set_premium:%s", redPacketUUID)),
							})
						} else {
							// Add "Cancel Premium" button
							cancelPremiumText := i18n.T(ctx, "{#CancelPremiumRedPacket}")
							rows = append(rows, []tgbotapi.InlineKeyboardButton{
								tgbotapi.NewInlineKeyboardButtonData(cancelPremiumText, fmt.Sprintf("rp:cancel_premium:%s", redPacketUUID)),
							})
						}

						shareKeyboard := tgbotapi.NewInlineKeyboardMarkup(rows...)
						finalKeyboard = &shareKeyboard // Set the keyboard for the success response
					}
				} // End of if createRedPacket

				// Clear state is already true
			}
		}
	} else {
		// --- Failure ---
		g.Log().Warningf(ctx, "handleRedPacketPasswordConfirm: Password verification failed for user %d: %v", userId, verificationErr)
		var pwdErr *shared.PaymentPasswordError
		if errors.As(verificationErr, &pwdErr) {
			responseText = pwdErr.Message // Use the localized message from the verifier
			switch pwdErr.Type {
			case shared.ErrPasswordIncorrect:
				// Keep state and allow retry
				clearState = false

				// Reset password in context for retry
				userState.Context[consts.RpContextKeyCurrentPassword] = ""
				err = service.UserState().SetUserStateByTelegramId(ctx, userId, userState) // Update state in Redis
				if err != nil {
					// If updating state fails, log it but proceed to show the error message anyway
					g.Log().Errorf(ctx, "handleRedPacketPasswordConfirm: Failed to reset password in user state for user %d after incorrect attempt: %v", userId, err)
				}

				// Format display and regenerate keyboard for retry (with empty password)
				resetPasswordDisplay := shared.FormatPasswordDisplay("") // Get "\n\n🔑 _ _ _ _ _ _"
				retryKeyboard := shared.GetNumericKeyboard("", consts.CallbackRedPacketPasswordKeyPrefix, false)
				finalKeyboard = &retryKeyboard // Keep keyboard for retry

				// Prepend error message to the prompt and include the reset password display
				promptText := i18n.T(ctx, "{#RedPacketEnterPasswordPrompt}") // Get the base prompt
				// Combine the error message (already in pwdErr.Message), the prompt, and the reset password display
				responseText = fmt.Sprintf("%s\n%s%s", pwdErr.Message, promptText, resetPasswordDisplay)
			case shared.ErrAccountLocked:
				// Clear state is already true
			default:
				// Other specific errors like NotSet, UserNotFound handled by verifier message
				// Clear state is already true
			}
		} else {
			// Generic internal error (from verifier or other issues)
			responseText = verificationErr.Error() // Use the error message directly
			// Clear state is already true
		}
	}

	// 6. Clear state if needed
	if clearState {
		err = service.UserState().ClearUserStateByTelegramId(ctx, userId)
		if err != nil {
			g.Log().Warningf(ctx, "handleRedPacketPasswordConfirm: Failed to clear user state for user %d after handling result: %v", userId, err)
			// Log warning but proceed with response
		}
	}

	// 7. Edit the original message (photo)
	var currentPhotoFileID string
	if cq.Message != nil && len(cq.Message.Photo) > 0 {
		currentPhotoFileID = cq.Message.Photo[len(cq.Message.Photo)-1].FileID
	} else {
		g.Log().Warningf(ctx, "handleRedPacketPasswordConfirm: Could not get photo FileID from callback message %d for user %d. Attempting text edit.", messageID, userId)
		// Fallback to editing text if photo ID is missing
		response := callback.NewExtendedEditMessageResponse(cq.ID, chatID, messageID, responseText, finalKeyboard)
		response.ParseMode = "HTML" // Enable HTML parsing for bold text
		return response, nil
	}
	// Use ReplaceWithPhotoResponse to update caption and potentially keep/remove keyboard
	response := callback.NewReplaceWithPhotoResponse(cq.ID, chatID, messageID, currentPhotoFileID, responseText, finalKeyboard)
	response.ParseMode = "HTML" // Enable HTML parsing for bold text
	return response, nil
}
