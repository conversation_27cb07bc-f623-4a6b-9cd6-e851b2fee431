package redpacket

import (
	"context"
	"fmt"
	"strconv"

	localConsts "telegram-bot-api/internal/consts"
	"telegram-bot-api/internal/dao"
	"telegram-bot-api/internal/middleware"
	"telegram-bot-api/internal/model"
	"telegram-bot-api/internal/model/callback"
	"telegram-bot-api/internal/model/entity"
	"telegram-bot-api/internal/service"

	tgbotapi "github.com/a19ba14d/telegram-bot-api/v5"
	"github.com/a19ba14d/tg-bot-common/consts"
	"github.com/gogf/gf/v2/frame/g"
)

// handleBackToTypeSelection handles returning to red packet type selection page
func handleBackToTypeSelection(ctx context.Context, cq *tgbotapi.CallbackQuery) (callback.CallbackResponse, error) {
	chatID := cq.Message.Chat.ID
	messageID := cq.Message.MessageID
	userID := cq.From.ID
	i18n := service.I18n().Instance()

	// Clear any existing user state
	_ = service.UserState().ClearUserStateByTelegramId(ctx, userID)

	g.Log().Infof(ctx, "handleBackToTypeSelection: User %d returning to red packet type selection", userID)

	// Get default token symbol from config
	defaultTokenSymbol := g.Cfg().MustGet(ctx, "red_packet.default_token_symbol", "CNY").String()

	// Create red packet type selection keyboard
	randomCallback := fmt.Sprintf("%s:%s", consts.CallbackSelectRedPacketTypeRandom, defaultTokenSymbol)
	fixedCallback := fmt.Sprintf("%s:%s", consts.CallbackSelectRedPacketTypeFixed, defaultTokenSymbol)

	keyboard := tgbotapi.NewInlineKeyboardMarkup(
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData(i18n.T(ctx, "{#RedPacketTypeRandomButton}"), randomCallback),
			tgbotapi.NewInlineKeyboardButtonData(i18n.T(ctx, "{#RedPacketTypeFixedButton}"), fixedCallback),
		),
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("↩️ "+i18n.T(ctx, "{#BackButton}"), localConsts.CallbackRedPacketBackToMain),
		),
	)

	// Delete current message and send new one
	response := callback.NewDeleteAndSendResponse(chatID, messageID, i18n.T(ctx, "{#SelectRedPacketTypeTitle}"), &keyboard)
	response.CallbackQueryID = cq.ID
	return response, nil
}

// handleSetMemo handles setting memo for red packet
func handleSetMemo(ctx context.Context, cq *tgbotapi.CallbackQuery, redPacketUUID string) (callback.CallbackResponse, error) {
	chatID := cq.Message.Chat.ID
	messageID := cq.Message.MessageID
	userID := cq.From.ID
	i18n := service.I18n().Instance()

	// Get red packet record
	var redPacket *entity.RedPackets
	err := dao.RedPackets.Ctx(ctx).
		Where("uuid", redPacketUUID).
		Where("creator_user_id", userID).
		Scan(&redPacket)

	if err != nil || redPacket == nil {
		g.Log().Errorf(ctx, "handleSetMemo: Failed to get red packet %s for user %d: %v", redPacketUUID, userID, err)
		return callback.NewAlertResponse(i18n.T(ctx, "{#ErrorRedPacketRecordNotFound}")), nil
	}

	// Set user state for memo input
	stateContext := map[string]string{
		"red_packet_uuid": redPacketUUID,
		"red_packet_id":   fmt.Sprintf("%d", redPacket.RedPacketId),
	}
	newState := model.NewUserState(localConsts.UserStateWaitingRedPacketMemo, stateContext, model.InputTypeText, 3, 600) // 10 minutes TTL
	newState.MessageIDToEdit = 0                                                                                         // Will delete and send new message
	errSet := service.UserState().SetUserStateByTelegramId(ctx, userID, newState)
	if errSet != nil {
		g.Log().Errorf(ctx, "handleSetMemo: Failed to set user state for user %d: %v", userID, errSet)
		return callback.NewAlertResponse(i18n.T(ctx, "{#SetUserStateError}")), nil
	}

	// Build memo input prompt
	promptText := i18n.T(ctx, "请输入备注信息(150字以内)")

	// Create keyboard with back button
	keyboard := tgbotapi.NewInlineKeyboardMarkup(
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData(
				"↩️ "+i18n.T(ctx, "返回"),
				fmt.Sprintf("rp:back_to_details:%s", redPacketUUID),
			),
		),
	)

	// Delete current message and send new prompt
	response := callback.NewDeleteAndSendResponse(chatID, messageID, promptText, &keyboard)
	response.CallbackQueryID = cq.ID
	return response, nil
}

// handleSetConditions handles displaying the conditions setting page for red packet
func handleSetConditions(ctx context.Context, cq *tgbotapi.CallbackQuery, redPacketUUID string) (callback.CallbackResponse, error) {
	chatID := cq.Message.Chat.ID
	messageID := cq.Message.MessageID
	userID := cq.From.ID
	i18n := service.I18n().Instance()

	// Get red packet record
	var redPacket *entity.RedPackets
	err := dao.RedPackets.Ctx(ctx).
		Where("uuid", redPacketUUID).
		Where("creator_user_id", userID).
		Scan(&redPacket)

	if err != nil || redPacket == nil {
		g.Log().Errorf(ctx, "handleSetConditions: Failed to get red packet %s for user %d: %v", redPacketUUID, userID, err)
		return callback.NewAlertResponse(i18n.T(ctx, "{#ErrorRedPacketRecordNotFound}")), nil
	}

	// Build conditions page text
	text := buildRedPacketConditionsText(ctx, redPacket)

	// Build conditions keyboard
	keyboard := buildRedPacketConditionsKeyboard(ctx, redPacket)

	// Delete current message and send new one
	response := callback.NewDeleteAndSendResponse(chatID, messageID, text, &keyboard)
	response.CallbackQueryID = cq.ID
	response.ParseMode = "HTML"
	return response, nil
}

// handleBackToDetails handles returning to red packet details page
func handleBackToDetails(ctx context.Context, cq *tgbotapi.CallbackQuery, redPacketUUID string) (callback.CallbackResponse, error) {
	chatID := cq.Message.Chat.ID
	messageID := cq.Message.MessageID
	userID := cq.From.ID
	i18n := service.I18n().Instance()

	// Clear user state
	_ = service.UserState().ClearUserStateByTelegramId(ctx, userID)

	// Get red packet record
	var redPacket *entity.RedPackets
	err := dao.RedPackets.Ctx(ctx).
		Where("uuid", redPacketUUID).
		Where("creator_user_id", userID).
		Scan(&redPacket)

	if err != nil || redPacket == nil {
		g.Log().Errorf(ctx, "handleBackToDetails: Failed to get red packet %s for user %d: %v", redPacketUUID, userID, err)
		return callback.NewAlertResponse(i18n.T(ctx, "{#ErrorRedPacketRecordNotFound}")), nil
	}

	// Build details page
	detailsText := BuildRedPacketDetailsText(ctx, redPacket)
	detailsKeyboard := BuildRedPacketDetailsKeyboard(ctx, redPacket.Uuid)

	// Check if red packet has cover
	if redPacket.CoverFileId != "" {
		// Delete current message and send photo with details
		response := callback.NewDeleteAndSendPhotoResponse(chatID, messageID, redPacket.CoverFileId, detailsText, &detailsKeyboard)
		response.CallbackQueryID = cq.ID
		return response, nil
	} else {
		// Delete current message and send text
		response := callback.NewDeleteAndSendResponse(chatID, messageID, detailsText, &detailsKeyboard)
		response.CallbackQueryID = cq.ID
		response.ParseMode = "HTML"
		return response, nil
	}
}

// buildRedPacketConditionsText builds the text for red packet conditions page
func buildRedPacketConditionsText(ctx context.Context, redPacket *entity.RedPackets) string {
	i18n := service.I18n().Instance()

	// Get telegram username from user_backup_accounts table
	var backupAccount *entity.UserBackupAccounts
	err := dao.UserBackupAccounts.Ctx(ctx).
		Where("telegram_id", redPacket.CreatorUserId).
		Where("is_master", 1).
		Scan(&backupAccount)

	username := redPacket.CreatorUsername // Default to stored username
	if err == nil && backupAccount != nil && backupAccount.TelegramUsername != "" {
		username = backupAccount.TelegramUsername
	}

	// Build basic text
	text := fmt.Sprintf(
		"🧧 <b>%s</b> %s\n"+
			"💵 <b>%s</b> %s %s\n"+
			"📊 <b>%s</b> %d/%d\n\n"+
			"⚙️ <b>%s</b>\n",

		username, i18n.T(ctx, "发送一个红包"),
		i18n.T(ctx, "总金额："), redPacket.TotalAmount.String(), redPacket.Symbol,
		i18n.T(ctx, "剩余："), redPacket.RemainingQuantity, redPacket.Quantity,
		i18n.T(ctx, "红包条件设置"),
	)

	// Add condition details for enabled conditions (green buttons)
	var conditionDetails []string

	// Check verification code condition
	if redPacket.IsVerificationCode == 1 {
		conditionDetails = append(conditionDetails, "🔐 "+i18n.T(ctx, "需要验证码才能领取"))
	}

	// Check premium condition
	if redPacket.IsPremium == 1 {
		conditionDetails = append(conditionDetails, "👑 "+i18n.T(ctx, "仅限会员用户领取"))
	}

	// Check group condition
	if redPacket.SpecifyGroup == 1 && redPacket.GroupId != "" && redPacket.GroupId != "0" {
		// Get tenant ID from context
		tenantId, ok := middleware.GetTenantIdFromContext(ctx)
		if ok {
			// Try to get the group info
			var telegramGroup *entity.TelegramGroups
			groupChatId, parseErr := strconv.ParseInt(redPacket.GroupId, 10, 64)
			if parseErr == nil {
				err := dao.TelegramGroups.Ctx(ctx).
					Where("chat_id", groupChatId).
					Where("tenant_id", tenantId).
					Scan(&telegramGroup)

				if err == nil && telegramGroup != nil {
					// Show group title and invitation link
					groupInfo := fmt.Sprintf("👥 "+i18n.T(ctx, "仅限指定群组成员领取")+"\n"+
						"   "+i18n.T(ctx, "群组: %s"), telegramGroup.Title)

					// Add invitation link if available
					if redPacket.GroupInvitationLink != "" {
						groupInfo += fmt.Sprintf("\n   "+i18n.T(ctx, "邀请链接: %s"), redPacket.GroupInvitationLink)
					} else if telegramGroup.InviteLink != "" {
						groupInfo += fmt.Sprintf("\n   "+i18n.T(ctx, "邀请链接: %s"), telegramGroup.InviteLink)
					}

					conditionDetails = append(conditionDetails, groupInfo)
				}
			}
		}
	}

	// Check betting volume condition
	if redPacket.SpecifyBetting == 1 && !redPacket.BettingVolume.IsZero() {
		timeText := GetBettingTimeText(ctx, redPacket.BettingVolumeDays)
		conditionDetails = append(conditionDetails, fmt.Sprintf("💰 "+i18n.T(ctx, "需要%s流水达到%s %s"),
			timeText, redPacket.BettingVolume.String(), redPacket.Symbol))
	}

	// Add condition details to text if any conditions are enabled
	if len(conditionDetails) > 0 {
		text += "\n" + i18n.T(ctx, "当前启用的条件：") + "\n"
		for _, detail := range conditionDetails {
			text += "• " + detail + "\n"
		}
	} else {
		text += "\n" + i18n.T(ctx, "暂无启用的条件，所有用户都可以领取") + "\n"
	}

	return text
}

// buildRedPacketConditionsKeyboard builds the keyboard for red packet conditions page
func buildRedPacketConditionsKeyboard(ctx context.Context, redPacket *entity.RedPackets) tgbotapi.InlineKeyboardMarkup {
	i18n := service.I18n().Instance()

	// Build condition status buttons
	verificationCodeStatus := getConditionStatus(redPacket.IsVerificationCode == 1)
	premiumStatus := getConditionStatus(redPacket.IsPremium == 1)
	groupStatus := getConditionStatus(redPacket.SpecifyGroup == 1)
	bettingStatus := getConditionStatus(redPacket.SpecifyBetting == 1)

	keyboard := tgbotapi.NewInlineKeyboardMarkup(
		// Verification code row
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData(
				"🔐 "+i18n.T(ctx, "验证码"),
				fmt.Sprintf("rp:toggle_verification:%s", redPacket.Uuid),
			),
			tgbotapi.NewInlineKeyboardButtonData(
				verificationCodeStatus,
				fmt.Sprintf("rp:toggle_verification:%s", redPacket.Uuid),
			),
		),
		// Premium red packet row
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData(
				"👑 "+i18n.T(ctx, "会员红包"),
				fmt.Sprintf("rp:toggle_premium:%s", redPacket.Uuid),
			),
			tgbotapi.NewInlineKeyboardButtonData(
				premiumStatus,
				fmt.Sprintf("rp:toggle_premium:%s", redPacket.Uuid),
			),
		),
		// Specific group row
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData(
				"👥 "+i18n.T(ctx, "指定群组"),
				fmt.Sprintf("rp:set_group:%s", redPacket.Uuid),
			),
			tgbotapi.NewInlineKeyboardButtonData(
				groupStatus,
				func() string {
					if redPacket.SpecifyGroup == 1 {
						return fmt.Sprintf("rp:close_group:%s", redPacket.Uuid)
					}
					return "rp:noop"
				}(),
			),
		),
		// Betting volume row
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData(
				"💰 "+i18n.T(ctx, "流水红包"),
				fmt.Sprintf("rp:set_betting:%s", redPacket.Uuid),
			),
			tgbotapi.NewInlineKeyboardButtonData(
				bettingStatus,
				func() string {
					if redPacket.SpecifyBetting == 1 {
						return fmt.Sprintf("rp:close_betting:%s", redPacket.Uuid)
					}
					return "rp:noop"
				}(),
			),
		),
		// Back button
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData(
				"↩️ "+i18n.T(ctx, "返回"),
				fmt.Sprintf("rp:back_to_details:%s", redPacket.Uuid),
			),
		),
	)

	return keyboard
}

// getConditionStatus returns the status string with emoji for a condition
func getConditionStatus(enabled bool) string {
	if enabled {
		return "✅ 已开启"
	}
	return "❌ 未开启"
}

// handleCloseDetails handles closing the red packet details page by deleting the message
func handleCloseDetails(ctx context.Context, cq *tgbotapi.CallbackQuery) (callback.CallbackResponse, error) {
	chatID := cq.Message.Chat.ID
	messageID := cq.Message.MessageID
	userID := cq.From.ID

	// Clear any existing user state
	_ = service.UserState().ClearUserStateByTelegramId(ctx, userID)

	g.Log().Infof(ctx, "handleCloseDetails: User %d closing red packet details", userID)

	// Return response to delete the message
	response := &callback.DeleteMessageResponse{
		CallbackQueryID: cq.ID,
		ChatID:          chatID,
		MessageID:       messageID,
	}
	return response, nil
}

// handleRedPacketDetailsFromList handles showing the details of a specific red packet from the list
func handleRedPacketDetailsFromList(ctx context.Context, cq *tgbotapi.CallbackQuery, redPacketUUID string, sourcePage int, sourceStatus string) (callback.CallbackResponse, error) {
	chatID := cq.Message.Chat.ID
	messageID := cq.Message.MessageID
	userID := cq.From.ID
	i18n := service.I18n().Instance()

	// Get red packet record
	var redPacket *entity.RedPackets
	err := dao.RedPackets.Ctx(ctx).
		Where("uuid", redPacketUUID).
		Scan(&redPacket)

	if err != nil || redPacket == nil {
		g.Log().Errorf(ctx, "handleRedPacketDetailsFromList: Failed to get red packet %s: %v", redPacketUUID, err)
		return callback.NewAlertResponse(i18n.T(ctx, "{#ErrorRedPacketRecordNotFound}")), nil
	}

	// Build details text using existing function
	detailsText := BuildRedPacketDetailsText(ctx, redPacket)

	// Build keyboard - use different keyboards based on whether user is creator
	var keyboard tgbotapi.InlineKeyboardMarkup
	if redPacket.CreatorUserId == userID {
		// For creators, show the full details keyboard with back to list button
		keyboard = buildRedPacketDetailsKeyboardWithBackToList(ctx, redPacket.Uuid, sourcePage, sourceStatus)
	} else {
		// For non-creators, show only back to list button
		keyboard = tgbotapi.NewInlineKeyboardMarkup(
			tgbotapi.NewInlineKeyboardRow(
				tgbotapi.NewInlineKeyboardButtonData(
					"🔙 "+i18n.T(ctx, "返回"),
					fmt.Sprintf("rp:back_to_list:%d:%s", sourcePage, sourceStatus),
				),
			),
		)
	}

	// Check if red packet has cover
	if redPacket.CoverFileId != "" {
		// Delete current message and send photo with details
		response := callback.NewDeleteAndSendPhotoResponse(chatID, messageID, redPacket.CoverFileId, detailsText, &keyboard)
		response.CallbackQueryID = cq.ID
		return response, nil
	} else {
		// Delete current message and send text
		response := callback.NewDeleteAndSendResponse(chatID, messageID, detailsText, &keyboard)
		response.CallbackQueryID = cq.ID
		response.ParseMode = tgbotapi.ModeHTML
		return response, nil
	}
}

// buildRedPacketDetailsKeyboardWithBackToList builds the keyboard for red packet details page with back to list button
func buildRedPacketDetailsKeyboardWithBackToList(ctx context.Context, redPacketUUID string, sourcePage int, sourceStatus string) tgbotapi.InlineKeyboardMarkup {
	i18n := service.I18n().Instance()

	// Get red packet record to check status
	var redPacket *entity.RedPackets
	err := dao.RedPackets.Ctx(ctx).
		Where("uuid", redPacketUUID).
		Scan(&redPacket)

	// Build keyboard rows based on status
	var rows [][]tgbotapi.InlineKeyboardButton

	// Only show action buttons if red packet is active
	if err == nil && redPacket != nil && redPacket.Status == string(consts.RedPacketStatusActive) {
		// First row: "再发一个" and "发送红包" buttons
		// 注意："再发一个"按钮只是触发新的创建流程（rp:add），不会复制当前红包的任何配置包括领取条件
		rows = append(rows, tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData(
				"🚀 "+i18n.T(ctx, "再发一个"), // 触发全新的红包创建流程，不复制任何配置
				"rp:add",                    // 调用创建红包处理器，开始新的创建流程
			),
			tgbotapi.NewInlineKeyboardButtonSwitch(
				"🚀 "+i18n.T(ctx, "发送红包"),
				consts.InlineQueryPrefixShareRedPacket+redPacketUUID,
			),
		))

		// Second row: Set memo and Set conditions
		rows = append(rows, tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData(
				"📝 "+i18n.T(ctx, "设置备注"),
				fmt.Sprintf("rp:set_memo:%s", redPacketUUID),
			),
			tgbotapi.NewInlineKeyboardButtonData(
				"⚙️ "+i18n.T(ctx, "设置条件"),
				fmt.Sprintf("rp:set_conditions:%s", redPacketUUID),
			),
		))
	}

	// Always show back to list button
	rows = append(rows, tgbotapi.NewInlineKeyboardRow(
		tgbotapi.NewInlineKeyboardButtonData(
			"🔙 "+i18n.T(ctx, "返回"),
			fmt.Sprintf("rp:back_to_list:%d:%s", sourcePage, sourceStatus),
		),
	))

	keyboard := tgbotapi.NewInlineKeyboardMarkup(rows...)
	return keyboard
}
