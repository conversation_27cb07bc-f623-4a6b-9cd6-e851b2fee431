package message

import (
	"context"
	"encoding/json"
	"regexp"
	"strconv"
	"strings"

	config "telegram-bot-api/internal/config"
	"telegram-bot-api/internal/dao"
	"telegram-bot-api/internal/model/entity"

	tgbotapi "github.com/a19ba14d/telegram-bot-api/v5"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/shopspring/decimal"

	"telegram-bot-api/internal/middleware"
	"telegram-bot-api/internal/service"
)

// FileConfig represents the structure of the file URL JSON
type FileConfig struct {
	URL  string `json:"url"`
	Name string `json:"name"`
	Size int    `json:"size"`
}

// GroupRedPacketCommand represents a parsed group red packet command
type GroupRedPacketCommand struct {
	Type             string          // "hb" for normal red packet, "xyhb" for lucky red packet
	Amount           decimal.Decimal // Total amount
	Quantity         int             // Number of red packets
	TokenSymbol      string          // Token symbol (default: CNY)
	Blessing         string          // Blessing message (optional)
	BettingVolume    decimal.Decimal // Betting volume requirement (optional)
	HasBettingVolume bool            // Whether betting volume is specified
}

// parseGroupRedPacketCommand parses group red packet commands
// Supported formats:
// hb 5CNY 200 - Random red packet: 5 CNY, 200 packets
// hb 100 5CNY 200 - Random red packet with betting volume: 100 betting volume, 5 CNY, 200 packets
// hb 100 5 USDT blessing - Random red packet: 100 USDT, 5 packets, with blessing (legacy format)
// xyhb 100 5 USDT blessing - Lucky red packet: 100 USDT, 5 packets, with blessing
// xyhb 500CNY 10 - Lucky red packet: 500 CNY, 10 packets (amount+currency format)
// xyhb 1000 500CNY 10 - Lucky red packet with betting volume: 1000 betting volume, 500 CNY, 10 packets
func parseGroupRedPacketCommand(text string) (*GroupRedPacketCommand, error) {
	text = strings.TrimSpace(text)
	parts := strings.Fields(text)

	if len(parts) < 3 {
		return nil, gerror.New("Invalid command format")
	}

	cmd := &GroupRedPacketCommand{
		TokenSymbol: "CNY", // Default to CNY
	}

	// Parse command type
	cmdType := strings.ToLower(parts[0])
	if cmdType != "hb" && cmdType != "xyhb" {
		return nil, gerror.New("Unsupported command type")
	}
	cmd.Type = cmdType

	// For both hb and xyhb commands, check if first parameter after command is all digits (betting volume)
	// Format: hb 1000 500CNY 10 [blessing] or xyhb 1000 500CNY 10 [blessing]
	var parsingIndex = 1
	if len(parts) >= 4 {
		// Try to parse first parameter as betting volume
		bettingVolumeStr := parts[1]
		if bettingVolume, err := decimal.NewFromString(bettingVolumeStr); err == nil && bettingVolume.GreaterThan(decimal.Zero) {
			// Check if next parameter contains amount with currency
			if strings.Contains(parts[2], "CNY") || (len(parts) > 4 && strings.ToUpper(parts[3]) == "CNY") {
				// This is betting volume format
				cmd.BettingVolume = bettingVolume
				cmd.HasBettingVolume = true
				parsingIndex = 2 // Start parsing amount from index 2
			}
		}
	}

	// Parse amount - support both "100" and "100CNY" formats
	amountStr := parts[parsingIndex]
	var amount decimal.Decimal
	var err error
	var amountParsed bool

	// Check if amount contains currency suffix (like "500CNY")
	if strings.Contains(amountStr, "CNY") {
		// Extract numeric part and currency
		numericPart := strings.TrimSuffix(amountStr, "CNY")
		numericPart = strings.TrimSuffix(numericPart, "cny")
		amount, err = decimal.NewFromString(numericPart)
		if err == nil {
			cmd.TokenSymbol = "CNY"
			amountParsed = true
		}
	}

	// If amount parsing failed above, try parsing as pure number
	if !amountParsed {
		amount, err = decimal.NewFromString(amountStr)
		if err != nil {
			return nil, gerror.New("Invalid amount format")
		}
	}

	if amount.LessThanOrEqual(decimal.Zero) {
		return nil, gerror.New("Amount must be greater than 0")
	}
	cmd.Amount = amount

	// Parse quantity
	quantityIndex := parsingIndex + 1
	if quantityIndex >= len(parts) {
		return nil, gerror.New("Invalid command format")
	}

	quantity, err := strconv.Atoi(parts[quantityIndex])
	if err != nil {
		return nil, gerror.New("Invalid quantity format")
	}
	if quantity <= 0 {
		return nil, gerror.New("Quantity must be greater than 0")
	}
	cmd.Quantity = quantity

	// Parse token symbol if not already parsed from amount
	blessingStartIndex := quantityIndex + 1

	if blessingStartIndex < len(parts) && !amountParsed {
		potentialToken := strings.ToUpper(parts[blessingStartIndex])
		if potentialToken == "CNY" {
			cmd.TokenSymbol = potentialToken
			blessingStartIndex++
		}
	}

	// Parse blessing (optional)
	if blessingStartIndex < len(parts) {
		blessing := strings.Join(parts[blessingStartIndex:], " ")
		cmd.Blessing = strings.TrimSpace(blessing)
	}

	return cmd, nil
}

// validateGroupRedPacketCommand validates the parsed command against system constraints
func validateGroupRedPacketCommand(ctx context.Context, cmd *GroupRedPacketCommand) error {
	// Validate token exists and is allowed for red packets
	token, err := service.Token().GetTokenBySymbol(ctx, cmd.TokenSymbol)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to get token info for %s: %v", cmd.TokenSymbol, err)
		return gerror.Newf("Unsupported token: %s", cmd.TokenSymbol)
	}
	if token == nil {
		return gerror.Newf("Unsupported token: %s", cmd.TokenSymbol)
	}

	// Check if token is allowed for red packets
	activeTokens, err := service.Token().GetActiveRedPacketTokens(ctx)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to get active red packet tokens: %v", err)
		return gerror.New("System error, please try again later")
	}

	tokenAllowed := false
	for _, activeToken := range activeTokens {
		if activeToken.Symbol == cmd.TokenSymbol {
			tokenAllowed = true
			break
		}
	}

	if !tokenAllowed {
		return gerror.Newf("Token %s does not support red packet feature", cmd.TokenSymbol)
	}

	// Validate minimum amount per red packet
	minAmount := decimal.NewFromFloat(0.01) // Minimum 0.01 per red packet
	avgAmount := cmd.Amount.DivRound(decimal.NewFromInt(int64(cmd.Quantity)), int32(token.Decimals))
	if avgAmount.LessThan(minAmount) {
		return gerror.Newf("Single red packet amount cannot be less than %s %s", minAmount.String(), cmd.TokenSymbol)
	}

	return nil
}

// getLocalizedCommandError converts command parsing errors to localized messages
func getLocalizedCommandError(ctx context.Context, err error) string {
	i18n := service.I18n().Instance()
	errMsg := err.Error()

	switch errMsg {
	case "Invalid command format":
		return i18n.T(ctx, "GroupRedPacketInvalidFormat")
	case "Unsupported command type":
		return i18n.T(ctx, "GroupRedPacketUnsupportedCommand")
	case "Invalid amount format":
		return i18n.T(ctx, "GroupRedPacketInvalidAmount")
	case "Amount must be greater than 0":
		return i18n.T(ctx, "GroupRedPacketAmountMustBePositive")
	case "Invalid quantity format":
		return i18n.T(ctx, "GroupRedPacketInvalidQuantity")
	case "Quantity must be greater than 0":
		return i18n.T(ctx, "GroupRedPacketQuantityMustBePositive")
	case "Invalid betting volume format":
		return i18n.T(ctx, "GroupRedPacketInvalidBettingVolume")
	case "Betting volume must be greater than 0":
		return i18n.T(ctx, "GroupRedPacketBettingVolumeMustBePositive")
	default:
		// For validation errors with parameters
		if strings.HasPrefix(errMsg, "Unsupported token:") {
			parts := strings.SplitN(errMsg, ": ", 2)
			if len(parts) > 1 {
				return i18n.Tf(ctx, "GroupRedPacketUnsupportedToken", parts[1])
			}
		} else if strings.HasPrefix(errMsg, "Token") && strings.Contains(errMsg, "does not support") {
			// Extract token symbol from "Token XXX does not support red packet feature"
			tokenPattern := regexp.MustCompile(`Token (\w+) does not support`)
			if matches := tokenPattern.FindStringSubmatch(errMsg); len(matches) > 1 {
				return i18n.Tf(ctx, "GroupRedPacketTokenNotEnabled", matches[1])
			}
		} else if strings.Contains(errMsg, "cannot be less than") {
			// Extract amount and token from error
			amountPattern := regexp.MustCompile(`cannot be less than ([0-9.]+) (\w+)`)
			if matches := amountPattern.FindStringSubmatch(errMsg); len(matches) > 2 {
				return i18n.Tf(ctx, "GroupRedPacketAmountTooSmall", matches[1], matches[2])
			}
		}
		// Default to system error
		return i18n.T(ctx, "GroupRedPacketSystemError")
	}
}

// sendMessageToGroup sends a message to a group using the tenant bot
func sendMessageToGroup(ctx context.Context, msg tgbotapi.MessageConfig) {
	// Get tenant ID from context
	tenantId, ok := middleware.GetTenantIdFromContext(ctx)
	if !ok {
		g.Log().Error(ctx, "Failed to get tenant ID from context for group message")
		return
	}

	// Get bot instance for the tenant
	bot, err := service.TenantBotManager().GetBot(ctx, tenantId)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to get bot instance for tenant %d: %v", tenantId, err)
		return
	}

	// Send the message
	_, err = bot.Send(msg)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to send message to group: %v", err)
	}
}

// handleGroupRedPacketCommand handles group red packet commands
func handleGroupRedPacketCommand(ctx context.Context, message *tgbotapi.Message) (bool, error) {
	telegramID := message.From.ID
	chatID := message.Chat.ID

	g.Log().Infof(ctx, "Processing group red packet command from user %d in chat %d: %s", telegramID, chatID, message.Text)

	// Parse the command
	cmd, err := parseGroupRedPacketCommand(message.Text)
	if err != nil {
		g.Log().Warningf(ctx, "Failed to parse group red packet command from user %d: %v", telegramID, err)
		// Send error message to the group
		localizedError := getLocalizedCommandError(ctx, err)
		errorMsg := tgbotapi.NewMessage(chatID, "❌ "+localizedError)
		errorMsg.ReplyToMessageID = message.MessageID
		sendMessageToGroup(ctx, errorMsg)
		return true, nil
	}

	// Validate the command
	if err := validateGroupRedPacketCommand(ctx, cmd); err != nil {
		g.Log().Warningf(ctx, "Group red packet command validation failed for user %d: %v", telegramID, err)
		// Send error message to the group
		localizedError := getLocalizedCommandError(ctx, err)
		errorMsg := tgbotapi.NewMessage(chatID, "❌ "+localizedError)
		errorMsg.ReplyToMessageID = message.MessageID
		sendMessageToGroup(ctx, errorMsg)
		return true, nil
	}

	// Get user information
	user, err := service.User().GetUserByTelegramId(ctx, telegramID)
	if err != nil || user == nil {
		g.Log().Errorf(ctx, "Failed to get user info for telegram ID %d: %v", telegramID, err)
		i18n := service.I18n().Instance()
		errorMsg := tgbotapi.NewMessage(chatID, "❌ "+i18n.T(ctx, "GroupRedPacketUserNotRegistered"))
		errorMsg.ReplyToMessageID = message.MessageID
		sendMessageToGroup(ctx, errorMsg)
		return true, nil
	}

	// Get token information to get token ID
	token, err := service.Token().GetTokenBySymbol(ctx, cmd.TokenSymbol)
	if err != nil || token == nil {
		g.Log().Errorf(ctx, "Failed to get token info for %s: %v", cmd.TokenSymbol, err)
		i18n := service.I18n().Instance()
		errorMsg := tgbotapi.NewMessage(chatID, "❌ "+i18n.T(ctx, "GroupRedPacketTokenInfoFailed"))
		errorMsg.ReplyToMessageID = message.MessageID
		sendMessageToGroup(ctx, errorMsg)
		return true, nil
	}

	// Get cover configuration based on red packet type
	var coverFileID string
	var thumbUrl string

	// Read red_packet_setting.luck_img configuration
	luckImgConfig, _ := config.GetString(ctx, "red_packet_setting.luck_img")
	if luckImgConfig == "" {
		g.Log().Error(ctx, "Missing red_packet_setting.luck_img in config")
		i18n := service.I18n().Instance()
		errorMsg := tgbotapi.NewMessage(chatID, "❌ "+i18n.T(ctx, "GroupRedPacketConfigError"))
		errorMsg.ReplyToMessageID = message.MessageID
		sendMessageToGroup(ctx, errorMsg)
		return true, nil
	}

	// Parse the JSON configuration to get the URL
	if strings.HasPrefix(strings.TrimSpace(luckImgConfig), "{") {
		var fileConfig FileConfig
		if err := json.Unmarshal([]byte(luckImgConfig), &fileConfig); err != nil {
			g.Log().Errorf(ctx, "Failed to parse red_packet_setting.luck_img JSON: %v, raw value: %s", err, luckImgConfig)
			i18n := service.I18n().Instance()
			errorMsg := tgbotapi.NewMessage(chatID, "❌ "+i18n.T(ctx, "GroupRedPacketConfigError"))
			errorMsg.ReplyToMessageID = message.MessageID
			sendMessageToGroup(ctx, errorMsg)
			return true, nil
		} else {
			thumbUrl = fileConfig.URL
			coverFileID = "" // Remove file_id logic, use URL only
			g.Log().Debugf(ctx, "Parsed luck_img config: URL=%s, Name=%s, Size=%d", fileConfig.URL, fileConfig.Name, fileConfig.Size)
		}
	} else {
		g.Log().Errorf(ctx, "red_packet_setting.luck_img is not a valid JSON: %s", luckImgConfig)
		i18n := service.I18n().Instance()
		errorMsg := tgbotapi.NewMessage(chatID, "❌ "+i18n.T(ctx, "GroupRedPacketConfigError"))
		errorMsg.ReplyToMessageID = message.MessageID
		sendMessageToGroup(ctx, errorMsg)
		return true, nil
	}

	// Determine red packet type based on command
	redPacketType := "random" // Default for "hb" command
	if cmd.Type == "hb" {
		redPacketType = "random"
	} else if cmd.Type == "xyhb" {
		redPacketType = "random" // For now, treat xyhb as random too
	}

	// Get telegram username from user_backup_accounts table
	var backupAccount *entity.UserBackupAccounts
	err = dao.UserBackupAccounts.Ctx(ctx).
		Where(dao.UserBackupAccounts.Columns().UserId, user.Id).
		Where(dao.UserBackupAccounts.Columns().IsMaster, 1).
		Scan(&backupAccount)

	creatorUsername := user.Account // Default fallback
	if err == nil && backupAccount != nil && backupAccount.TelegramUsername != "" {
		creatorUsername = backupAccount.TelegramUsername
	}

	// Create red packet input
	createInput := service.CreateRedPacketInput{
		UserId:            telegramID,
		CreatorUserId:     telegramID,
		CreatorUsername:   creatorUsername,
		ChatId:            chatID,
		TokenId:           uint(token.TokenId),
		TokenSymbol:       cmd.TokenSymbol,
		Type:              redPacketType,
		Quantity:          cmd.Quantity,
		TotalAmount:       cmd.Amount,
		Blessing:          cmd.Blessing,
		RedPacketImagesId: 0,           // Use default
		CoverFileID:       coverFileID, // Empty string, no file_id logic
		ThumbUrl:          thumbUrl,    // Use URL from config
		MessageId:         message.MessageID,
		// Group red packet specific fields
		RedPacketType:    "group",  // Set as group red packet
		CommandType:      cmd.Type, // hb or xyhb
		IsGroupRedPacket: true,     // Flag as group red packet
		// 领取条件设置 - 从命令解析得出，不复制历史红包
		BettingVolume:     cmd.BettingVolume, // 从用户命令中解析的流水金额要求
		BettingVolumeDays: 0,                 // 流水天数（当前实现固定为0，表示总流水）
		SpecifyBetting:    0,                 // 是否需要流水（下面根据HasBettingVolume设置）
	}

	// 根据是否有流水要求设置SpecifyBetting标志
	// 注意：这个值不会从历史红包复制，完全基于当前命令
	if cmd.HasBettingVolume {
		createInput.SpecifyBetting = 1
	}

	// Call CreateRedPacketV2 service
	redPacket, err := service.RedPacket().CreateRedPacketV2(ctx, createInput)
	if err != nil {
		// Convert technical error messages to user-friendly Chinese messages
		userErrorMsg := convertToUserFriendlyError(ctx, err.Error(), cmd.TokenSymbol)

		// Log as warning for validation errors, error for system errors
		if isValidationError(err.Error()) {
			g.Log().Warningf(ctx, "Group red packet validation failed for user %d: %v", telegramID, err)
		} else {
			g.Log().Errorf(ctx, "Failed to create group red packet for user %d: %v", telegramID, err)
		}

		errorMsg := tgbotapi.NewMessage(chatID, "❌ "+userErrorMsg)
		errorMsg.ReplyToMessageID = message.MessageID
		sendMessageToGroup(ctx, errorMsg)
		return true, nil
	}

	g.Log().Infof(ctx, "Group red packet created successfully: UUID=%s, Type=%s, Amount=%s, Quantity=%d",
		redPacket.Uuid, cmd.Type, cmd.Amount.String(), cmd.Quantity)

	// Send the interactive red packet message
	err = sendGroupRedPacketMessage(ctx, redPacket)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to send group red packet message for %s: %v", redPacket.Uuid, err)
		// Send fallback text message
		i18n := service.I18n().Instance()
		fallbackText := "🎁 " + i18n.T(ctx, "GroupRedPacketCreatedSuccess") + "\n"
		fallbackText += "💰 " + i18n.T(ctx, "GroupRedPacketAmount") + "：" + cmd.Amount.String() + " " + cmd.TokenSymbol + "\n"
		fallbackText += "📦 " + i18n.T(ctx, "GroupRedPacketQuantity") + "：" + g.NewVar(cmd.Quantity).String() + " " + i18n.T(ctx, "GroupRedPacketUnit") + "\n"
		fallbackText += "🎯 " + i18n.T(ctx, "GroupRedPacketType") + "：" + getRedPacketTypeText(ctx, cmd) + "\n"
		if cmd.HasBettingVolume {
			fallbackText += "🎲 " + i18n.T(ctx, "GroupRedPacketBettingVolume") + "：" +
				i18n.Tf(ctx, "GroupRedPacketBettingVolumeFormat", cmd.BettingVolume.String(), cmd.TokenSymbol) + "\n"
		}
		if cmd.Blessing != "" {
			fallbackText += "💌 " + i18n.T(ctx, "GroupRedPacketBlessing") + "：" + cmd.Blessing + "\n"
		}
		fallbackText += "🆔 " + i18n.T(ctx, "GroupRedPacketId") + "：" + redPacket.Uuid

		fallbackMsg := tgbotapi.NewMessage(chatID, fallbackText)
		fallbackMsg.ReplyToMessageID = message.MessageID
		sendMessageToGroup(ctx, fallbackMsg)
	}

	return true, nil
}

// getRedPacketTypeText returns the display text for red packet type
func getRedPacketTypeText(ctx context.Context, cmd *GroupRedPacketCommand) string {
	i18n := service.I18n().Instance()
	if cmd.HasBettingVolume {
		return i18n.T(ctx, "GroupRedPacketTypeBettingVolume")
	}
	switch cmd.Type {
	case "hb":
		return i18n.T(ctx, "GroupRedPacketTypeNormal")
	case "xyhb":
		return i18n.T(ctx, "GroupRedPacketTypeLucky")
	default:
		return i18n.T(ctx, "GroupRedPacketTypeUnknown")
	}
}

// getRedPacketLimits retrieves red packet configuration limits from the config system
func getRedPacketLimits(ctx context.Context) (minAmount decimal.Decimal, maxQuantity int) {
	// Get minimum amount from config (default: 0.01)
	minAmountFloat, err := config.GetDecimal(ctx, "red_packet_setting.min_single_amount.default", decimal.NewFromFloat(0.01))
	if err != nil {
		g.Log().Warningf(ctx, "Failed to get min_single_amount from config: %v, using default 0.01", err)
		minAmount = decimal.NewFromFloat(0.01)
	} else {
		minAmount = minAmountFloat
	}

	// Get max quantity from config (default: 100)
	maxQuantity, err = config.GetInt(ctx, "red_packet_setting.group_max_num", 100)
	if err != nil {
		g.Log().Warningf(ctx, "Failed to get group_max_num from config: %v, using default 100", err)
		maxQuantity = 100
	}

	return minAmount, maxQuantity
}

// convertToUserFriendlyError converts technical error messages to user-friendly localized messages
func convertToUserFriendlyError(ctx context.Context, errorMsg, tokenSymbol string) string {
	i18n := service.I18n().Instance()

	// Get configuration limits
	minAmount, maxQuantity := getRedPacketLimits(ctx)

	// Extract numeric values from error messages for parameterized i18n
	var amount string
	var quantity int
	var currentBalance, requiredAmount string

	// Parse minimum amount from error message
	if matches := regexp.MustCompile(`cannot be less than ([0-9.]+)`).FindStringSubmatch(errorMsg); len(matches) > 1 {
		amount = matches[1]
	} else {
		// Use configured minimum amount as fallback
		amount = minAmount.String()
	}

	// Parse quantity from error message
	if matches := regexp.MustCompile(`cannot exceed ([0-9]+)`).FindStringSubmatch(errorMsg); len(matches) > 1 {
		quantity, _ = strconv.Atoi(matches[1])
	} else if matches := regexp.MustCompile(`must be at least ([0-9]+)`).FindStringSubmatch(errorMsg); len(matches) > 1 {
		quantity, _ = strconv.Atoi(matches[1])
	}

	// Parse balance information from Chinese error message
	if matches := regexp.MustCompile(`当前余额=([0-9.]+), 需要金额=([0-9.]+)`).FindStringSubmatch(errorMsg); len(matches) > 2 {
		currentBalance = matches[1]
		requiredAmount = matches[2]
	}

	// Error mapping with i18n support
	errorPatterns := []struct {
		pattern string
		i18nKey string
		params  []interface{}
	}{
		{
			pattern: "Average group red packet amount cannot be less than",
			i18nKey: "GroupRedPacketAvgAmountTooLow",
			params:  []interface{}{amount, tokenSymbol},
		},
		{
			pattern: "Single fixed red packet amount cannot be less than",
			i18nKey: "GroupRedPacketSingleAmountTooLow",
			params:  []interface{}{amount, tokenSymbol},
		},
		{
			pattern: "Average random red packet amount cannot be less than",
			i18nKey: "GroupRedPacketAvgAmountTooLow",
			params:  []interface{}{amount, tokenSymbol},
		},
		{
			pattern: "Group red packet quantity cannot exceed",
			i18nKey: "GroupRedPacketQuantityExceeded",
			params:  []interface{}{quantity},
		},
		{
			pattern: "Red packet quantity must be at least",
			i18nKey: "GroupRedPacketQuantityTooLow",
			params:  []interface{}{quantity},
		},
		{
			pattern: "Red packet quantity cannot exceed",
			i18nKey: "GroupRedPacketQuantityExceeded",
			params:  []interface{}{maxQuantity}, // Use configured max quantity
		},
		{
			pattern: "Total red packet amount must be greater than 0",
			i18nKey: "GroupRedPacketAmountZero",
			params:  nil,
		},
		{
			pattern: "Insufficient balance",
			i18nKey: "GroupRedPacketInsufficientBalance",
			params:  nil,
		},
		{
			pattern: "余额不足",
			i18nKey: "GroupRedPacketInsufficientBalance",
			params:  nil,
		},
		{
			pattern: "Daily red packet creation limit reached",
			i18nKey: "GroupRedPacketDailyLimitReached",
			params:  nil,
		},
		{
			pattern: "Daily red packet amount limit reached",
			i18nKey: "GroupRedPacketDailyAmountLimitReached",
			params:  nil,
		},
		{
			pattern: "You have been temporarily restricted from creating red packets",
			i18nKey: "GroupRedPacketTemporarilyRestricted",
			params:  nil,
		},
		{
			pattern: "Too many failed attempts",
			i18nKey: "GroupRedPacketFailedAttemptsTooMany",
			params:  nil,
		},
	}

	// Special handling for insufficient balance with details
	if strings.Contains(errorMsg, "余额不足") && currentBalance != "" && requiredAmount != "" {
		return i18n.Tf(ctx, "GroupRedPacketInsufficientBalanceDetailed",
			currentBalance, tokenSymbol, requiredAmount, tokenSymbol)
	}

	// Match error patterns and return localized message
	for _, ep := range errorPatterns {
		if strings.Contains(errorMsg, ep.pattern) {
			// Skip the detailed balance pattern if we already handled it above
			if ep.pattern == "余额不足" && ep.i18nKey == "GroupRedPacketInsufficientBalanceDetailed" {
				continue
			}
			if len(ep.params) > 0 {
				return i18n.Tf(ctx, ep.i18nKey, ep.params...)
			}
			return i18n.T(ctx, ep.i18nKey)
		}
	}

	// Default fallback for unknown errors
	return i18n.T(ctx, "GroupRedPacketSystemError")
}

// isValidationError checks if the error is a validation error (not a system error)
func isValidationError(errorMsg string) bool {
	validationPatterns := []string{
		"cannot be less than",
		"cannot exceed",
		"must be at least",
		"must be greater than",
		"Daily red packet",
		"temporarily restricted",
		"failed attempts",
		"Insufficient balance",
		"Red packet quantity",
		"Total red packet amount",
		"Average group red packet amount",
		"Single fixed red packet amount",
		"Average random red packet amount",
		"余额不足", // Chinese insufficient balance
		"当前余额", // Chinese current balance
	}

	for _, pattern := range validationPatterns {
		if strings.Contains(errorMsg, pattern) {
			return true
		}
	}

	return false
}
